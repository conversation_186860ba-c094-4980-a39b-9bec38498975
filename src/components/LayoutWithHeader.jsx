'use client';

import Header from '@/components/Header';
import { useContext } from 'react';
import { AppContext } from '../hooks/useAppContext';

export default function LayoutWithHeader({ children }) {
  const { isFromAida, isWorkspaceReady } = useContext(AppContext);

  return (
    <div className="grow relative flex flex-col overflow-y-auto overflow-x-hidden bg-gray-100">
      {!isFromAida && <Header />}
      <div className="flex h-full bg-gray-100 border-t border-gray-200 overflow-hidden">
        {(isFromAida || isWorkspaceReady) && children}
      </div>
    </div>
  );
}
