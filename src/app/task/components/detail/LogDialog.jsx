import { useState } from 'react';
import { Modal, Tooltip, Radio, Button, Spin, Card } from 'antd';
import { InfoCircleTwoTone } from '@ant-design/icons';
import axios from 'axios';

/**
 * 执行日志弹窗
 * @param {0 | 1} type 日志类型 0-模型 1-指标
 * @param {number} queryDetailId
 */
export default function LogDialog({ visible, type, queryDetailId, queryId, onClose }) {
  const [loading, setLoading] = useState(false);
  const [info, setInfo] = useState({
    basicInfo: {},
    modelConfig: {},
    prompt: '',
    invokeInfo: {},
  });
  // prompt | invokeInfo
  const [activeTab, setActiveTab] = useState('prompt');

  function MyOnclose() {
    setInfo({
      basicInfo: {},
      modelConfig: {},
      prompt: '',
      invokeInfo: {},
    });
    onClose();
  }

  async function getInfo() {
    setLoading(true);
    try {
      const { prompt, modelConfig, invokeInfo, basicInfo } = await axios.get('/api/aigc/eval/task/log', {
        params: {
          type,
          queryDetailId,
          queryId,
        }
      });
      setInfo({
        prompt: prompt || {},
        modelConfig: modelConfig || {},
        invokeInfo: invokeInfo || {},
        basicInfo: basicInfo || {},
      });
    } catch {
      // do nothing
    } finally {
      setLoading(false);
    }
  }

  function titleRender() {
    return (
      <div className='flex justify-between items-center'>
        <div className='flex gap-10'>
          <div>花费时间：{info.basicInfo.costTime}</div>
          <div>花费token：{info.basicInfo.costToken}</div>
        </div>
        <Tooltip
          placement="bottom"
          color='#eee'
          overlayClassName='!max-w-none'
          title={(
            <div className='w-300 flex flex-col gap-10 text-black'>
              {
                Object.keys(info.modelConfig.modelParam || {}).map((key) => (
                  <div key={key} className='flex justify-between'>
                    <span>{key}</span>
                    <span>{info.modelConfig.modelParam[key]}</span>
                  </div>
                ))
              }
            </div>
          )}
        >
          <Button icon={<InfoCircleTwoTone />}>{info.modelConfig.modelName}</Button>
        </Tooltip>
      </div>
    )
  }

  return (
    <Modal
      title={titleRender()}
      open={visible}
      afterOpenChange={(open) => {
        if (open) {
          getInfo();
        } else {
          MyOnclose();
        }
      }}
      centered
      okButtonProps={{
        classNames: {
          display: 'none',
        },
      }}
      footer={() => (
        <div className='text-right'>
          <Button onClick={() => MyOnclose()}>关闭</Button>
        </div>
      )}
      closeIcon={false}
    >
      <Spin spinning={loading}>
        <div className='flex flex-col gap-10'>
          <Radio.Group onChange={(e) => setActiveTab(e.target.value)} value={activeTab}>
            <Radio.Button value="prompt">提示词配置</Radio.Button>
            <Radio.Button value="invokeInfo">真实调用</Radio.Button>
          </Radio.Group>
          {
            activeTab === 'prompt' && (
              <Card size='small'>
                <div className='max-h-600 overflow-y-auto'>
                  { info.prompt.content }
                </div>
              </Card>
            )
          }
          {
            activeTab === 'invokeInfo' && (
              <>
                <Card title="历史上下文" size='small' key='历史上下文'>
                  <div className='max-h-300 overflow-y-auto'>
                    { info.invokeInfo.history }
                  </div>
                </Card>
                <Card title="真实输出" size='small' key='真实输出'>
                  <div className='max-h-300 overflow-y-auto'>
                    { info.invokeInfo.output }
                  </div>
                </Card>
              </>
            )
          }
        </div>
      </Spin>
    </Modal>
  )
}
