#目标：将原始前端应用的在线会话分析功能迁移到新应用的会话查询

1.原始应用说明
原始应用目录位置：case-analysis-temp/src-vue
待迁移模块：case-analysis-temp/src-vue/src/views/analysis-tools


2.新应用说明
新应用目录位置：case-analysis-fe/case-loop/src/views/analysis
会话查询功能入口位置：
case-analysis-fe/case-loop/src/views/analysis/SessionAnalysis.vue


# 迁移功能说明
1. 新应用的会话查询页面，对应于原始应用的在线会话分析页面。迁移后的样式使用新应用的样式。
2. 仔细分析主页面筛选项与筛选项配置（/api/aigc/eval/workbench/session/condition/config）的联动代码、获取所有场景关联的空间/应用/task等信息（/api/aigc/eval/workbench/session/condition/scene）对主页面筛选项的影响逻辑，主页面上筛选项及筛选项之间的联动关系（api/aigc/eval/workbench/session/condition/task/versions，/api/aigc/eval/workbench/session/condition/task/nodes）。注意不要修改后端代码。
3. 若你在迁移过程中遇到前端代码不兼容的情况，请使用新应用的框架进行改错。
4. 注意新应用已具备的能力，不用迁移。