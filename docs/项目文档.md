# AI搭系统客服案例分析平台文档

## 项目概述

AI搭系统客服案例分析平台是一款专注于智能客服领域的大模型应用平台，用于数据标注、评测管理和应用搭建。本平台提供了完整的客服案例分析能力，帮助用户更高效地管理和优化智能客服系统。

## 技术栈

- **前端框架**：Vue 3.2.13 + TypeScript + Vite
- **UI组件库**：Ant Design Vue 3.2.20
- **构建工具**：Vue CLI 5.0.0
- **CSS预处理器**：Less 4.3.0
- **图标库**：@ant-design/icons-vue 7.0.1
- **代码规范工具**：ESLint、Prettier
- **测试框架**：Vue Test Utils
- **状态管理**：Pinia

## 项目结构

```
case-loop/
├── public/             # 静态资源目录
├── src/                # 源代码目录
│   ├── assets/         # 资源文件(图片、字体等)
│   ├── components/     # 通用组件
│   ├── App.vue         # 根组件
│   └── main.ts         # 入口文件
├── .gitignore          # Git忽略配置
├── babel.config.js     # Babel配置
├── tsconfig.json       # TypeScript配置
├── package.json        # 项目依赖
├── README.md           # 项目说明
└── vue.config.ts       # Vue配置
```

## 功能模块

项目包含三个主要功能模块：

### 1. 数据标注
- 仪表盘：数据标注概览
- 数据管理：包含数据埋点规则和数据获取
- 标注管理：包含任务管理、数据标注和标注质检

### 2. 评测管理
- 评测概览：评测数据总览
- 模型评测：包含基准测试和自定义测试
- 人工评测：包含评测任务和评测报告

### 3. 应用搭建
- 应用概览：应用数据总览
- 模型管理：包含模型库和版本管理
- 提示词工程：包含提示词模板和提示词测试
- 应用创建：包含可视化搭建和应用部署

## 代码规范

### 组件设计原则

1. **单一职责原则**：每个组件只负责单一功能，复杂功能应拆分为多个子组件
2. **组件大小控制**：每个组件文件不超过400行代码
3. **代码结构清晰**：组件内部结构分为template、script、style三部分

### 命名规范

1. **文件命名**：使用PascalCase（首字母大写）
2. **组件名**：使用PascalCase
3. **变量命名**：使用camelCase（首字母小写）
4. **CSS类名**：使用kebab-case（短横线）

### 编码风格

1. **避免深层嵌套**：使用提前返回策略减少if嵌套
2. **使用组合API**：使用Vue 3的组合API（Composition API）进行状态管理
3. **统一UI组件**：使用Ant Design Vue组件库，保持界面一致性

## 开发指南

### 环境准备

```bash
# 安装依赖
npm install

# 开发环境运行
npm run serve

# 生产环境构建
npm run build

# 代码检查
npm run lint
```

### 新增组件开发流程

1. 在`src/components`目录下创建新组件
2. 组件使用单文件组件(SFC)格式(.vue)
3. 组件必须有明确的props定义和注释
4. 大型组件应拆分为多个子组件

### UI设计规范

项目使用了自定义的Ant Design Vue主题，主要颜色为：

- 主色：#1890ff
- 成功色：#52c41a
- 警告色：#faad14
- 错误色：#f5222d
- 文本主色：rgba(0, 0, 0, 0.65)

## 部署指南

1. 执行`npm run build`生成生产环境代码
2. 将`dist`目录下的文件部署到Web服务器
3. 确保服务器配置支持Vue Router的历史模式（需要配置适当的重定向规则）

## 扩展计划

未来版本将着重于以下方面的改进：

1. 引入状态管理库(Pinia)处理复杂状态
2. 集成TypeScript提升代码质量和可维护性
3. 增强数据可视化能力，集成图表库
4. 优化移动端适配，提升响应式布局能力 


## 重要模块说明
### 会话查询

#### 筛选项
1. 筛选项配置
筛选项支持自定义配置，所有筛选项配置从API（/api/aigc/eval/workbench/session/condition/config?channel=online）中读取。选中后的筛选项会显示在主页面上（记录到浏览器本地缓存中），随着筛选项的增减而更新。
2. 筛选项与API联动逻辑：通过/api/aigc/eval/workbench/session/condition/scene获取的结果，sceneName用于筛选项场景（该筛选项始终不是禁用的）的可选值列表，workspaceAppList和applicationList用于筛选项空间/应用的可选值列表；buInfos字段里的buName，subBuName用于业务和子业务的可选值列表；taskKeyInfos用于筛选项触发task的可选值列表。
3. 筛选项选择某个值后，需要以标签的形式显示在已选择筛选项模块，支持单个删除，支持保存到常用筛选（调用API）。
4. 常用筛选，获取到已保存的筛选项组合的记录列表，选中某一个时直接应用到主页面的筛选项（将对应的筛选项赋值）；支持删除某一记录。
5. 主页面上显示的筛选项存在依赖关系。
标准问和触发task只有选择了业务和子业务这两个筛选项后才变为启用状态。对于触发task来说，若没有选择业务和子业务，则触发task无法选择，依赖触发task的筛选项也无法选择；若触发task未选择，依赖它的触发task版本和触发task节点都无法选择；触发task节点又依赖触发task版本的选择。



