/**
 * 会话分析字段枚举
 * 统一管理所有字段的定义、显示名称和类型
 */

// 字段类型枚举
export enum FieldType {
  // 普通字段 - 由普通表单元素处理，需要在主组件中监听变化
  NORMAL = 'normal',
  // 组件字段 - 由自定义组件处理，组件内部会触发change事件
  COMPONENT = 'component',
}

// 字段定义接口
export interface FieldDefinition {
  // 字段ID，用于表单数据和API交互
  id: string;
  // 字段显示名称
  displayName: string;
  // 字段类型
  type: FieldType;
  // API ID，用于与后端交互
  apiId?: string;
}

// 会话分析字段枚举
export const SessionField: Record<string, FieldDefinition> = {
  // 基础字段
  SESSION_ID: {
    id: 'sessionId',
    displayName: '会话ID',
    type: FieldType.NORMAL,
    apiId: 'conversationId',
  },
  USER_ID: {
    id: 'userId',
    displayName: '用户ID',
    type: FieldType.NORMAL,
    apiId: 'userId',
  },
  DATE: {
    id: 'date',
    displayName: '创建时间',
    type: FieldType.NORMAL,
    apiId: 'createTime',
  },
  ORDER_ID: {
    id: 'orderId',
    displayName: '订单ID',
    type: FieldType.NORMAL,
    apiId: 'orderId',
  },
  WORKSPACE_AND_APP: {
    id: 'workspaceAndApp',
    displayName: '空间/应用',
    type: FieldType.COMPONENT,
  },
  INSPECTION_STATUS: {
    id: 'inspectionStatus',
    displayName: '质检状态',
    type: FieldType.NORMAL,
  },

  // 在线会话特有字段
  MODEL_NAME: {
    id: 'modelName',
    displayName: '模型名称',
    type: FieldType.NORMAL,
    apiId: 'modelName',
  },
  MODEL_VERSION: {
    id: 'modelVersion',
    displayName: '模型版本',
    type: FieldType.NORMAL,
    apiId: 'modelVersion',
  },
  USER_TYPE: {
    id: 'userType',
    displayName: '用户类型',
    type: FieldType.NORMAL,
    apiId: 'userType',
  },
  ORDER_STATUS: {
    id: 'orderStatus',
    displayName: '订单状态',
    type: FieldType.NORMAL,
    apiId: 'orderStatus',
  },
  MESSAGE_COUNT: {
    id: 'messageCount',
    displayName: '消息数量',
    type: FieldType.NORMAL,
    apiId: 'messageCount',
  },
  RESPONSE_TIME: {
    id: 'responseTime',
    displayName: '响应时间',
    type: FieldType.NORMAL,
    apiId: 'responseTime',
  },

  // 动态添加的特殊字段
  SCENE: {
    id: 'scene',
    displayName: '场景',
    type: FieldType.COMPONENT,
    apiId: 'scene',
  },
  APP_VERSION: {
    id: 'appVersion',
    displayName: '应用版本',
    type: FieldType.COMPONENT,
    apiId: 'appVersion',
  },

  // 其他普通字段
  VISIT_ID: {
    id: 'visitId',
    displayName: 'VisitID',
    type: FieldType.NORMAL,
  },
  QUESTION_NAME: {
    id: 'questionName',
    displayName: '标准问',
    type: FieldType.NORMAL,
  },
  SESSION_SOLVED: {
    id: 'sessionSolved',
    displayName: '问题是否解决',
    type: FieldType.NORMAL,
  },
  STARS: {
    id: 'stars',
    displayName: '满意度',
    type: FieldType.NORMAL,
  },
  IS_EXCEPTION_END: {
    id: 'isExceptionEnd',
    displayName: '是否异常退出',
    type: FieldType.NORMAL,
  },
  TRANSFER_STAFF: {
    id: 'transferStaff',
    displayName: '转人工',
    type: FieldType.NORMAL,
  },
  IS_TIMEOUT_END: {
    id: 'isTimeoutEnd',
    displayName: '是否超时退出',
    type: FieldType.NORMAL,
  },

  // 触发task相关字段
  TASK_KEY: {
    id: 'taskKey',
    displayName: '触发task',
    type: FieldType.COMPONENT,
    apiId: 'taskKey',
  },
  TASK_VERSION: {
    id: 'taskVersion',
    displayName: '触发task版本',
    type: FieldType.COMPONENT,
    apiId: 'taskVersion',
  },
  TASK_NODE: {
    id: 'taskNode',
    displayName: '触发task节点',
    type: FieldType.COMPONENT,
    apiId: 'taskNode',
  },

  // 业务相关字段
  BU: {
    id: 'bu',
    displayName: '业务',
    type: FieldType.COMPONENT,
  },
  SUB_BU: {
    id: 'subBu',
    displayName: '子业务',
    type: FieldType.COMPONENT,
  },

  // 电话会话特有字段
  CALL_DURATION: {
    id: 'callDuration',
    displayName: '通话时长',
    type: FieldType.NORMAL,
    apiId: 'callDuration',
  },
  CALLER_NUMBER: {
    id: 'callerNumber',
    displayName: '主叫号码',
    type: FieldType.NORMAL,
    apiId: 'callerNumber',
  },
  CALLEE_NUMBER: {
    id: 'calleeNumber',
    displayName: '被叫号码',
    type: FieldType.NORMAL,
    apiId: 'calleeNumber',
  },
};

/**
 * 获取所有普通字段的ID列表
 * @returns 普通字段ID列表
 */
export function getNormalFieldIds(): string[] {
  return Object.values(SessionField)
    .filter(field => field.type === FieldType.NORMAL)
    .map(field => field.id);
}

/**
 * 获取所有组件字段的ID列表
 * @returns 组件字段ID列表
 */
export function getComponentFieldIds(): string[] {
  return Object.values(SessionField)
    .filter(field => field.type === FieldType.COMPONENT)
    .map(field => field.id);
}

/**
 * 获取字段名称映射表
 * @returns 字段ID到显示名称的映射
 */
export function getFieldNameMap(): Record<string, string> {
  return Object.values(SessionField).reduce((map, field) => {
    map[field.id] = field.displayName;
    return map;
  }, {} as Record<string, string>);
}

/**
 * 获取API ID映射表
 * @returns 字段ID到API ID的映射
 */
export function getApiIdMap(): Record<string, string> {
  return Object.values(SessionField)
    .filter(field => field.apiId)
    .reduce((map, field) => {
      if (field.apiId) {
        map[field.id] = field.apiId;
      }
      return map;
    }, {} as Record<string, string>);
}

/**
 * 根据字段ID获取字段定义
 * @param id 字段ID
 * @returns 字段定义，如果不存在则返回undefined
 */
export function getFieldById(id: string): FieldDefinition | undefined {
  return Object.values(SessionField).find(field => field.id === id);
}

/**
 * 根据字段ID获取字段显示名称
 * @param id 字段ID
 * @returns 字段显示名称，如果不存在则返回原ID
 */
export function getFieldDisplayName(id: string): string {
  const field = getFieldById(id);
  return field ? field.displayName : id;
}

/**
 * 根据API ID获取字段ID
 * @param apiId API ID
 * @returns 字段ID，如果不存在则返回原API ID
 */
export function getFieldIdByApiId(apiId: string): string {
  const field = Object.values(SessionField).find(field => field.apiId === apiId);
  return field ? field.id : apiId;
}
