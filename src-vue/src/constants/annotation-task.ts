/**
 * @file 标注任务模块常量
 */

export enum TaskType {
  /**
   * 人工模拟
   */
  MANUAL_SIMULATION = 1,
  /**
   * 人工标注
   */
  MANUAL_ANNOTATION = 2,
  /**
   * 训练集标注
   */
  TRAIN_SET_ANNOTATION = 3,
}

/**
 * 标注任务映射
 */
export const TaskTypeMap = {
  [TaskType.MANUAL_SIMULATION]: {
    text: '人工模拟',
  },
  [TaskType.MANUAL_ANNOTATION]: {
    text: '人工标注',
  },
  [TaskType.TRAIN_SET_ANNOTATION]: {
    text: '训练集标注',
  },
};

/**
 * 标注目标类型枚举
 */
export enum MarkTargetType {
  /** 单选 */
  RADIO = 'radio',
  /** 下拉选择 */
  SELECT = 'select',
  /** 多选 */
  MULTI_SELECT = 'multi_select',
  /** 文本输入 */
  INPUT = 'input',
  /** 多行文本 */
  TEXTAREA = 'textarea',
}
