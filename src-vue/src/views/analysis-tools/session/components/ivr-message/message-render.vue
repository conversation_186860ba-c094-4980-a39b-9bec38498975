<template>
  <Component :is="getMessageComponent(props.ivrOriginMessage)" :ivr-origin-message="props.ivrOriginMessage" />
</template>

<script setup lang="ts">
import { IvrOperationTypeCode } from '@/constants/session-chat';
import VoiceBroadcastMessage from './voice-broadcast-message.vue';
import ContentInputMessage from './content-input-message.vue';
import TransferStaffMessage from './transfer-staff-message.vue';
import NotSupportMessage from './not-support-message.vue';

const props = defineProps<{
  ivrOriginMessage: IvrOriginMessageType;
}>();

const MessageTypeMap = {
  [IvrOperationTypeCode.VOICE_BROADCAST]: VoiceBroadcastMessage,
  [IvrOperationTypeCode.CONTENT_INPUT]: ContentInputMessage,
  [IvrOperationTypeCode.TRANSFER_STAFF]: TransferStaffMessage,
};

const getMessageComponent = (message: IvrOriginMessageType) => {
  const component = MessageTypeMap[message.operationTypeCode];
  if (!component) {
    return NotSupportMessage;
  }
  return component;
};
</script>

<style lang="scss" scoped></style>
