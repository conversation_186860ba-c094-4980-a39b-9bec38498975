<template>
  <mtd-select v-model="localValue" placeholder="请选择场景" :clearable="true">
    <mtd-option v-for="option in sceneOptions" :key="option.value" :value="option.value" :label="option.label">
      {{ option.label }}
    </mtd-option>
  </mtd-select>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

const props = defineProps<{
  modelValue?: string; // v-model 传递的值
  formData?: Record<string, any>; // 搜索表单数据
  sceneRelationConfig?: {
    sceneInfos: Array<{
      sceneId: number;
      sceneName: string;
      buInfos: Array<{
        buId: number;
        buName: string;
        buCode: string;
        subBuInfos: Array<{
          subBuId: number;
          subBuName: string;
          subBuCode: string;
          taskKeyInfos: Array<{
            taskKey: string;
          }>;
        }>;
      }>;
      workspaceAppList: Array<{
        workspaceId: string;
        workspaceName: string;
        applicationList?: Array<{
          applicationId: string;
          applicationName: string;
          applicationType: string;
          robotList?: Array<{
            robotId: string;
            robotName: string;
          }>;
        }>;
      }>;
    }>;
  } | null;
}>();

const emit = defineEmits<{
  (e: 'change', value: string, displayName?: string): void;
}>();

const localValue = ref(props.modelValue || '');

// 监听 props.modelValue 的变化，更新 localValue
watch(
  () => props.modelValue,
  (newValue) => {
    localValue.value = newValue || ''; // 更新本地值
  }
);

// 监听 props.formData.scene 的变化，更新 localValue 并触发 change 事件
watch(
  () => props.formData?.scene,
  (newValue, oldValue) => {
    // 只有当新值与旧值不同时才更新本地值和触发 change 事件
    if (newValue !== oldValue && newValue !== localValue.value) {
      localValue.value = newValue || ''; // 更新本地值

      // 如果新值不为空，手动触发 change 事件
      if (newValue) {
        // 获取场景的显示名称
        let displayName = newValue;
        if (props.sceneRelationConfig && props.sceneRelationConfig.sceneInfos) {
          const sceneInfo = props.sceneRelationConfig.sceneInfos.find(
            (scene) => String(scene.sceneId) === String(newValue)
          );
          if (sceneInfo) {
            displayName = sceneInfo.sceneName;
          }
        }

        emit('change', newValue, displayName); // 触发 change 事件
      }
    }
  },
  { immediate: true } // 立即触发一次，确保初始化时也能正确处理
);

watch(
  () => localValue.value,
  (newValue) => {
    // 获取场景的显示名称
    let displayName = newValue;

    if (newValue && props.sceneRelationConfig && props.sceneRelationConfig.sceneInfos) {
      const sceneInfo = props.sceneRelationConfig.sceneInfos.find((scene) => {
        return String(scene.sceneId) === String(newValue);
      });

      if (sceneInfo) {
        displayName = sceneInfo.sceneName;
      }
    }

    emit('change', newValue, displayName); // 更新父组件的值，并传递显示名称
  }
);

// 从 sceneRelationConfig 获取场景选项
const sceneOptions = computed(() => {
  if (!props.sceneRelationConfig || !props.sceneRelationConfig.sceneInfos) {
    return [];
  }

  const scenes = new Map<string, string>();

  // 收集所有场景信息
  props.sceneRelationConfig.sceneInfos.forEach((scene) => {
    // 使用 sceneId 作为值，sceneName 作为标签
    scenes.set(String(scene.sceneId), scene.sceneName);
  });

  const options = Array.from(scenes.entries()).map(([value, label]) => ({
    value,
    label: label || value,
  }));
  return options;
});

// 存储值到名称的映射，供外部访问
// 定义为响应式对象，便于外部访问
const sceneValueMap = ref<Record<string, string>>({});

// 对外暴露获取名称的方法
const getSceneName = (value: string): string => {
  // 先从映射中查找
  if (sceneValueMap.value[value]) {
    return sceneValueMap.value[value];
  }

  // 如果映射中没有，尝试从场景配置中查找
  if (props.sceneRelationConfig && props.sceneRelationConfig.sceneInfos) {
    const sceneInfo = props.sceneRelationConfig.sceneInfos.find((scene) => String(scene.sceneId) === String(value));
    if (sceneInfo) {
      // 将找到的名称添加到映射中
      sceneValueMap.value[value] = sceneInfo.sceneName;
      return sceneInfo.sceneName;
    }
  }

  // 如果都没有找到，返回原始值
  return value;
};

// 将方法挂载到组件实例上
defineExpose({
  getSceneName,
  sceneValueMap,
});

// 监听场景配置变化，重新加载场景选项
watch(
  () => props.sceneRelationConfig,
  (newConfig) => {
    // 更新值到名称的映射
    if (newConfig && newConfig.sceneInfos) {
      newConfig.sceneInfos.forEach((scene) => {
        sceneValueMap.value[String(scene.sceneId)] = scene.sceneName;
      });
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped></style>
