<template>
  <div class="container">
    <mtd-radio-group v-show="isAnalysisToolsRoute" v-model="currentTool" type="slider" class="tool-type" size="large">
      <template v-for="(item, index) in toolsList">
        <mtd-radio-button :value="item.type" :key="index" :disabled="item.disabled">{{ item.name }}</mtd-radio-button>
      </template>
    </mtd-radio-group>

    <div class="content" :class="{ 'with-margin': isAnalysisToolsRoute }">
      <Component v-if="SessionTypeComponentMap[currentTool] && isAnalysisToolsRoute && isSessionActive" :is="SessionTypeComponentMap[currentTool]" />
      <router-view></router-view>
    </div>
  </div>
</template>

<script lang="ts">
export default { name: 'AnalysisTools' };
</script>

<script setup lang="ts">
import IvrSession from './session/ivr-session.vue';
import OnlineSession from './session/online-session.vue';
import { SessionTypeCode } from '@/constants/session';
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router/composables';

const route = useRoute();
// session类型与对应组件的映射
const SessionTypeComponentMap: Record<string, Vue.Component> = {
  [SessionTypeCode.ONLINE]: OnlineSession,
  [SessionTypeCode.IVR]: IvrSession,
};

// Tool list with online session analysis, ivr analysis, and a placeholder for future tools
const toolsList = ref([
  {
    name: '在线会话分析',
    disabled: false,
    type: SessionTypeCode.ONLINE,
  },
  {
    name: '电话会话分析',
    disabled: false,
    type: SessionTypeCode.IVR,
  },
]);

const currentTool = ref(SessionTypeCode.ONLINE);

const isSessionActive = computed(
  () => currentTool.value === SessionTypeCode.ONLINE || currentTool.value === SessionTypeCode.IVR
);
const isAnalysisToolsRoute = computed(() => {
  const path = route.path;
  return path === '/vue/analysis/tools' || path === '/analysis/tools';
});
</script>

<style lang="scss" scoped>
.tool-type {
  align-self: flex-start;
}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 默认内容区域没有顶部边距 */
.content {
  flex: 1;
  overflow-y: auto;
  position: relative;
  height: calc(100% - 40px);
}

/* 只在主路由下添加边距 */
.content.with-margin {
  margin-top: 24px;
  height: calc(100% - 64px);
}
</style>