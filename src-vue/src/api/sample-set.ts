import axios from 'axios';

// 获取样本集详情
export const getSampleSetInfoApi = async (datasetId: number | string): Promise<SampleSetInfo> => {
  const { code, data, message } = (await axios.get('/api/aigc/eval/dataset/info', {
    params: { datasetId },
  })) as unknown as {
    code: number;
    data: SampleSetInfo;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

// 获取样本集分页数据
export const getSampleSetDetailPageApi = async (params: {
  datasetId: number | string;
  pageSize: number;
  pageNum: number;
}): Promise<SampleSetDetailResponse> => {
  const { code, data, message } = (await axios.get('/api/aigc/eval/dataset/detail/page', {
    params,
  })) as unknown as {
    code: number;
    data: SampleSetDetailResponse;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }

  return data;
};

// 删除样本集中的某条数据
export const deleteSampleSetDetailApi = async (params: { datasetId: number | string; id: number }): Promise<void> => {
  const { code, message } = (await axios.delete('/api/aigc/eval/dataset/detail/delete', {
    params,
  })) as unknown as {
    code: number;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }
};

/**
 * 更新样本集字段
 */
export const updateSampleSetFieldApi = async (data: { detailId: number; fieldKey: string; fieldValue: string }) => {
  const { code, message } = (await axios.post('/api/aigc/eval/dataset/detail/updateField', data)) as unknown as {
    code: number;
    message: string;
  };

  if (code !== 0) {
    throw new TypeError(message || '请求失败');
  }
};
