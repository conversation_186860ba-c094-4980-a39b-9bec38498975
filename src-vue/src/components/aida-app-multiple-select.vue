<template>
  <mtd-cascader
    class="ai-da-app-select-container"
    :data="formattedOption"
    :model-value="applicationList"
    checked-strategy="children"
    clearable
    multiple
    filterable
    @change="onAppSelect"
    placeholder="空间-机器人-机器人版本"
  />
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount, defineProps, defineEmits, defineExpose } from 'vue';
import { useStore } from '@/store';
import { Message } from '@ss/mtd-vue2';
import { getAiDaAppInfo } from '@/api/auto-evaluation';
import { getIsFromIframe, getIframeExtendsData } from '@/utils/open-across-iframe';
import { robotTypeOptions } from '@/constants/auto-evaluation';

interface Props {
  /**
   * ## 应用初始值 applicationList为应用数组，每一个application为应用信息数组，每一项分别对应：
   * * application[0]: 空间id
   * * application[1]: 应用id
   * * application[2]: 版本id
   * * application[3]: 版本名称
   */
  applicationList: Array<[string, string, string]> | [];
  /**
   * # 是否只支持选择指定id的应用的版本
   */
  targetApplicationId?: string;
  /**
   * # 是否只显示当前空间的应用
   */
  filterByCurrentSpace?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  applicationList: () => [],
  targetApplicationId: undefined,
  filterByCurrentSpace: false,
});

const emit = defineEmits<{
  (e: 'update:applicationList', value: string[][]): void;
  (e: 'change', value: Array<[string, string, string, string]>): void;
}>();

const store = useStore();
const currSpaceId = computed(() => store.getters['space/currSpaceId']);

// 获取应用类型的中文标签
const getRobotTypeLabel = (type: string) => {
  if (!type) return '';
  // 匹配不同类型的应用
  const option = robotTypeOptions.find((option) => option.value === type);
  return option ? option.label : type;
};

// 当前空间内的ai搭应用信息（级联选择器配置）
const formattedOption = ref<SampleCascaderOption>([]);
const rawApplicationList = ref<TAiDaAppInfo>([]);

// 获取ai搭机器人数据
const getAidaRobot = async () => {
  try {
    const res = await getAiDaAppInfo();
    const rawData = res?.data || [];
    rawApplicationList.value = rawData;
    // 考虑通过ai搭访问评测的场景，需要计算当前的空间id：如果是ai搭访问评测，则优先使用@/utils/open-across-iframe中的空间信息；否则使用store中的空间信息
    const currentSpaceId = getIsFromIframe() ? getIframeExtendsData().workspace?.id : currSpaceId.value;
    // 根据filterByCurrentSpace判断是否过滤掉非当前空间下的ai搭应用
    const filteredSpaceApp = props.filterByCurrentSpace
      ? rawData.filter(({ workspaceId }) => workspaceId === currentSpaceId)
      : rawData;
    // 将ai搭应用数据转换为级联选择器所需的数据结构
    formattedOption.value = filteredSpaceApp.map(({ workspaceId, workspaceName, applicationList }) => {
      let allApplicationList = applicationList || [];
      // 如果指定了目标应用id，则只支持选择指定的应用，所以需要过滤掉其他的应用
      if (props.targetApplicationId) {
        allApplicationList = allApplicationList.filter(
          ({ applicationId }) => applicationId === props.targetApplicationId
        );
      }
      const res = {
        label: workspaceName,
        value: workspaceId, // 保持字符串类型
        children: allApplicationList.map(({ applicationId, applicationName, applicationType, robotList }) => {
          // 根据应用类型获取中文标签
          const typeLabel = getRobotTypeLabel(applicationType);
          return {
            // 在标签中直接协合应用类型
            label: typeLabel ? `【${typeLabel}】${applicationName}` : applicationName,
            value: applicationId, // 保持字符串类型
            type: applicationType,
            children: (robotList || []).map(({ robotId, robotName }) => ({
              label: robotName,
              value: robotId, // 保持字符串类型
              children: null,
            })),
          };
        }),
      };
      return res;
    });
  } catch {
    Message.error('获取ai搭机器人数据失败~');
  }
};

// 暴露給外部组件，根据三个id获取机器人名称
const getRobotVersionName = (workspaceId: string, applicationId: string, robotId: string) => {
  // 先找空间
  const workspace = rawApplicationList.value.find((item) => item.workspaceId === workspaceId);
  if (!workspace) return '';
  // 再找应用
  const application = workspace.applicationList.find((item) => item.applicationId === applicationId);
  if (!application) return '';
  // 最后找机器人
  const robot = application.robotList.find((item) => item.robotId === robotId);
  return robot?.robotName || '';
};

// 暴露給外部组件，根据空间id和应用id找到对应应用，返回其名称
const getApplicationName = (workspaceId: string, applicationId: string) => {
  // 先找空间
  const workspace = rawApplicationList.value.find((item) => item.workspaceId === workspaceId);
  if (!workspace) return '';
  // 再找应用
  const application = workspace.applicationList.find((item) => item.applicationId === applicationId);
  if (!application) return '';
  return { applicationName: application.applicationName, workspaceName: workspace.workspaceName };
};

const onAppSelect = (applicationList: string[][]) => {
  const applicationListWithVersionName = applicationList.map((item) => {
    const [workspaceId, applicationId, robotId] = item;
    const versionName = getRobotVersionName(workspaceId, applicationId, robotId);
    return [...item, versionName];
  });
  emit('update:applicationList', applicationListWithVersionName);
  emit('change', applicationListWithVersionName);
};

// 使用 defineExpose 暴露方法和数据
defineExpose({
  rawApplicationList,
  getRobotVersionName,
  getApplicationName,
});

// 组件创建时获取数据
onBeforeMount(() => {
  getAidaRobot();
});
</script>

<style lang="scss" scoped>
.ai-da-app-select-container {
  // 增加级联选择器宽度以展示完整placeholder提示
  &.mtd-cascader {
    width: 100%;
  }
}
</style>
