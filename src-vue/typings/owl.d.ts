type owlType = 'start' | 'addError' | 'resetPv' | 'sendErrors' | 'setDimension';

type Instance = {
  connectType: string;
  firstCategory: string;
  logContent: string;
  pageUrl: string;
  project: string;
  realUrl: string;
  requestbyte: string;
  resourceUrl: string;
  responsebyte: string;
  responsetime: string;
  secondCategory?: string;
  statusCode: string;
  timestamp: string;
  traceid?: string;
  type: string;
};

interface OwlInterface {
  (
    type: 'start',
    config: {
      onBatchPush?(instance: Instance): boolean;
      [key: string]: unknown;
    }
  ): void;
  /**
   * 其他配置：https://km.sankuai.com/page/1288040496
   */
  (type: Exclude<owlType, 'start'>, ...args: unknown[]): void;
}

// eslint-disable-next-line @typescript-eslint/naming-convention
interface Window {
  owl: OwlInterface;
}
