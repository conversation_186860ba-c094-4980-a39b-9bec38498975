/**
 * 方法参考文档链接 https://km.sankuai.com/collabpage/2495996203
 */
declare module '@cs/logicForest-core' {
  export type GraphData = {
    nodes: {
      id: string;
      type: import('@/constants/ruleTask').TaskNodeEdgeType;
      x: number;
      y: number;
      text?: { x: number; y: number; value: string };
      properties?: Record<string, unknown> & {
        calledElement: import('@/constants/ruleTask').TaskRuleBusinessType;
        secondLevelNodeType?: import('@/constants/ruleTask').TaskRuleBusinessType;
      };
    }[];
    edges: Array<{
      id: string;
      type: string;
      sourceNodeId: string;
      targetNodeId: string;
      startPoint: unknown;
      endPoint: unknown;
      text: {
        x: number;
        y: number;
        value: string;
      };
      properties: Record<string, unknown>;
    }>;
  };

  export type NodeClickEvent = (event: 'node:click', callback: ({ data: NodeType }) => void) => void;
  export type GraphTransformEvent = (
    event: 'graph:transform',
    callback: ({ transform: { SCALE_X: number } }) => void
  ) => void;

  export type BlankClickEvent = (event: 'blank:click', callback: () => void) => void;

  export default class LogicForest<T extends Record<string, unknown> = Record<string, unknown>> {
    constructor(config: T): LogicForest;
    graphModel: {
      transformModel: {
        SCALE_X: number;
        HtmlPointToCanvasPoint([x, y]: [number, number]): [x: number, y: number];
        translate(x: number, y: number): void;
      };
      nodes: Array<NodeType>;
      updateCallActivity: (nodeId: string, content: Array<ParallelNodeContent>, height: number) => void;
      setDndShapeList: (panel: LogicForestPanelItem[]) => void;
    };
    setTipTheme(config: {
      nodeTipTheme: {
        stroke: string;
        strokeWidth: string;
        fill: string;
        color: string;
      };
      edgeTipTheme: {
        stroke: string;
        strokeWidth: string;
      };
    }): void;

    render(params: { nodes: Array<unknown>; edges: Array<unknown> }): void;

    focusOn: ({ id: string }) => void;

    rollbackAllTipIdList: () => void;

    // getNodeModelById: (id: string) => {
    //   getGroupNodeById: () => GraphData['nodes'][0];
    //   children: Set<string>;
    // };

    setTipIdList: (idList: Array<string>) => void;

    // /**
    //  * 销毁画布中的指定DOM元素
    //  * @param data 要销毁的DOM
    //  */
    // destroyBlockInCanvas: (data: { dom: HTMLElement }) => void;

    // getGraphData: () => GraphData;

    /**
     * 画布缩放方法
     * @param isZoom 是否放大，true为放大，false为缩小
     */
    zoom: (isZoom: boolean) => void;

    /** 画布api，画布按比例放大或缩小 */
    zoomToScale: (isZoom: boolean, scale: number) => void;

    /** 画布API，画布自适应布局 */
    fitViewInCommon: () => void;

    // /** 画布节点一键编排 */
    layoutInCommon: (data: { type: string; isFitView: boolean }) => void;

    /** 画布API，切换为3.0样式 */
    updateStyleToCommon: () => void;

    /** 画布API，设置节点样式 */
    setCommonStyle: (data: Logic3CommonStyleType) => void;

    /** 画布API，复制节点 */
    copy: () => void;

    /** 画布API，黏贴节点 */
    paste: () => void;

    /** 画布API，删除节点 */
    deleteElements: () => void;

    /** 画布API，撤销 */
    undo: () => void;

    /** 画布API，重做 */
    redo: () => void;

    /** 画布API，获取复制节点，可用来判断是否可粘贴 */
    getCopyedData: () =>
      | {
          edges: GraphData['edges'];
          node: GraphData['nodes'][0];
        }[]
      | undefined;

    /** 画布API，清空画布剪贴板 */
    clearShortcutSelectedData: () => void;

    /** 画布API，切换线段类型 */
    switchLinesTo: (lineType: 'bezier' | 'smooth', id: string) => void;

    /** 画布API，重置线段的坐标： */
    resetLineStartPoint: () => void;

    history: {
      /** 画布API，清空操作历史 */
      clearData: () => void;
      undoAble: () => boolean;
      redoAble: () => boolean;
    };

    /**
     * 画布API，聚焦到指定节点，同时触发当前节点的选中样式
     * 和focusOn不同的是 focusOn不会触发选中当前节点的选中样式
     * @param id 节点ID
     */
    moveToElement: (id: string) => void;

    /** 取消全部路径高亮 */
    rollbackPathAllTipIdList: () => void;

    /** 设置路径高亮 */
    setPathTipIdList: (idList: string[]) => void;

    /** 取消自定义路径高亮 */
    rollbackAllCommonPathTip: () => void;

    /** 设置自定义路径高亮 */
    setCommonPathTip: (options: {
      nodes?: Record<
        string,
        {
          block?: Record<string, string>;
          anchor?: Record<string, string>;
        }
      >;
      edges?: Record<
        string,
        {
          block: Record<string, string>;
          anchor: Record<string, string>;
        }
      >;
    }) => void;

    /** 画布API，设置节点/边删除的校验回调 */
    setCommonDeleteCallBack: (data: { node: () => boolean; edge: () => boolean }) => void;

    extension: {
      /** 画布api，销毁小地图 */
      commonMiniMap: {
        show: (data: { bottom?: number; top?: number; right?: number; left?: number }) => void;
        destroy: () => void;
      };
      /**  画布api，销毁左侧菜单栏 */
      CommonDndPanel: {
        destroy: () => void;
      };
      selectionSelect: {
        openSelectionSelect: () => void; // 开启框选
        closeSelectionSelect: () => void; // 结束框选
      };
    };

    on: NodeClickEvent & GraphTransformEvent & BlankClickEvent;

    /**
     * 画布API，在指定节点上展示自定义菜<.单
     * @param data
     * @returns
     */
    showBlockInNode: (data: {
      id: string;
      placement: 'top-end' | 'top-start' | 'bottom-end' | 'bottom-start';
      dom: HTMLElement;
      top?: number;
      left?: number;
      notAutoHide?: boolean;
    }) => string;

    /**
     * 画布API，销毁指定节点上的自定义菜<.单
     * @param id
     */
    closeBlockInNode: (id: string) => void;
  }
}

type LogicForestStyleState = {
  titleBackground?: string;
  titleColor?: string;
  fontSize?: number;
  fontWeight?: string;
  nodeBackground?: string;
  nodeDescColor?: string;
  borderColor?: string;
  borderWidth?: number;
  outlineWidth?: number;
  outlineColor?: string;
  borderRadius?: string;
  notAllowDeleteTitleColor?: string;
  boxShadow?: string;
};

type LogicForestEdgeType = {
  strokeColor: string;
  strokeWidth: string;
  showOutline?: boolean;
  outlineStrokeColor?: string;
  outlineStrokeWidth?: string;
  outlineStrokeDasharray?: string;
};

type LogicForestAnchorStyle = {
  radius?: number;
  borderColor?: string;
  fillColor?: string;
  iconColor?: string;
  iconFont?: string;
};

type LogicForestNodeStyle = {
  default: LogicForestStyleState;
  selected: LogicForestStyleState;
  hover: LogicForestStyleState;
};

type Logic3CommonStyleType = {
  node: {
    [key in TaskNodeEdgeType | TaskRuleBusinessType]?: LogicForestNodeStyle;
  };
  edge: {
    smooth: {
      default: LogicForestEdgeType;
      selected: LogicForestEdgeType;
      hover: LogicForestEdgeType;
    };
    bezier: {
      default: LogicForestEdgeType;
      selected: LogicForestEdgeType;
      hover: LogicForestEdgeType;
    };
  };
  dragEdge: {
    strokeColor: string;
    strokeWidth: string;
    isDash: boolean;
    dashStrokeColor: string;
    dashStrokeWidth: string;
    dashStrokeDasharray: string;
  };
  snapline: {
    width: string;
    color: string;
    strength: number;
  };
  selectedArea: {
    borderWidth: number;
    borderStyle: string;
    borderColor: string;
    backgroundColor: string;
  };
  edgePiece: {
    fill: string;
  };
  'circle-anchor': {
    [key in TaskNodeEdgeType]?: {
      default: LogicForestAnchorStyle;
      fill: LogicForestAnchorStyle;
      selected: LogicForestAnchorStyle;
      hover: LogicForestAnchorStyle;
    };
  };
  'rect-anchor': {
    [key in TaskNodeEdgeType]?: {
      default: LogicForestAnchorStyle;
      selected: LogicForestAnchorStyle;
      hover: LogicForestAnchorStyle;
    };
  };
};

type LogicForestPanelItem = {
  id: number;
  type: string;
  tipMsg: string;
  properties: {
    calledElement?:
      | import('@/constants/ruleTask').TaskNodeEdgeType
      | import('@/constants/ruleTask').TaskRuleBusinessType;
    name?: string;
  };
  icon: string;
  contentSlot: HTMLElement;
};

type ParallelNodeContent = {
  icon: string;
  title: string;
  id: string;
};

type DomAttributes = {
  class?: string;
  id?: string;
  text?: string | number;
  onclick?: (event: Event) => void;
  style?: Partial<HTMLElement['style']>;
} & Record<string, unknown>;

type VirtualDomRender = (
  element: keyof HTMLElementTagNameMap | 'foreignObject',
  attr: DomAttributes | null,
  children?: Array<ReturnType<VirtualDomRender>>
) => void;

type NodeType = {
  id: string;
  type: import('@/constants/ruleTask').TaskNodeEdgeType;
  x: number;
  y: number;
  properties: Record<string, unknown> & {
    content?: Array<ParallelNodeContent>;
    style?: Array<Partial<HTMLElement['style']>>;
  };
  text: Record<string, unknown>;
  getProperties: () => { style: Array<Partial<HTMLElement['style']>> } & Record<string, unknown>;
  setProperties: (config: { style: Array<Partial<HTMLElement['style']>> } & Record<string, unknown>) => void;
};
/**
 * history:change 事件里data的参数配置（暂未使用）
 *  */
type LogicForestHistory = {
  redoAble: boolean;
  redos: LogicForestRedoUndoType[];
  undoAble: boolean;
  undos: LogicForestRedoUndoType[];
};

type LogicForestRedoUndoType = {
  edges: {
    endPonit: { x: number; y: number };
    id: string;
    pointsList: { x: number; y: number }[];
    properties: { edgeType: string };
    sourceNodeId: string;
    startPoint: { x: number; y: number };
    targetNodeId: string;
    type: string;
  }[];
  isAutoLayoutModeInCommon: boolean;
  nodes: {
    id: string;
    properties: { bpmnH: number; bpmnW: number };
    text: { value: string; x: number; y: number };
    type: string;
    x: number;
    y: number;
  }[];
};

type LogicForestTextUpdateType = {
  id: string;
  text: string;
  type: string;
};
