<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab切换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .tabs {
            display: flex;
            border-bottom: 2px solid #e8e8e8;
            margin-bottom: 20px;
        }
        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        .tab.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }
        .tab:hover {
            color: #1890ff;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            background: #fafafa;
        }
        .tab-content.active {
            display: block;
        }
        .log {
            background: #f6f6f6;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 12px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 8px;
            padding: 4px 8px;
            border-radius: 3px;
        }
        .log-info {
            background: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        .log-success {
            background: #f6ffed;
            border-left: 3px solid #52c41a;
        }
        .log-warning {
            background: #fffbe6;
            border-left: 3px solid #faad14;
        }
        .timestamp {
            color: #666;
            font-size: 12px;
        }
        .clear-btn {
            background: #ff4d4f;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        .clear-btn:hover {
            background: #ff7875;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>会话分析页面 Tab 切换测试</h1>
        <p>这个测试模拟了SessionAnalysis.vue中的Tab切换行为，验证SessionFavorite组件是否会在激活时正确加载数据。</p>
        
        <div class="tabs">
            <button class="tab active" data-tab="query">会话查询</button>
            <button class="tab" data-tab="favorite">会话收藏</button>
        </div>
        
        <div class="tab-content active" id="query-content">
            <h3>会话查询页面</h3>
            <p>这里是会话查询的内容。当前Tab是激活状态。</p>
            <p>点击"会话收藏"Tab来测试SessionFavorite组件的数据加载。</p>
        </div>
        
        <div class="tab-content" id="favorite-content">
            <h3>会话收藏页面</h3>
            <p>这里是SessionFavorite组件的内容。</p>
            <p><strong>预期行为：</strong></p>
            <ul>
                <li>当切换到此Tab时，应该触发收藏数据的加载</li>
                <li>在控制台中应该看到相关的日志输出</li>
                <li>组件的active prop应该从false变为true</li>
            </ul>
            <div id="favorite-status">
                <p><strong>组件状态：</strong></p>
                <p>Active: <span id="active-status">false</span></p>
                <p>数据加载次数: <span id="load-count">0</span></p>
            </div>
        </div>
        
        <div class="log">
            <button class="clear-btn" onclick="clearLog()">清空日志</button>
            <div id="log-content"></div>
        </div>
    </div>

    <script>
        let loadCount = 0;
        let currentTab = 'query';
        
        // 模拟SessionFavorite组件的行为
        function simulateSessionFavorite(active) {
            const activeStatus = document.getElementById('active-status');
            const loadCountElement = document.getElementById('load-count');
            
            if (active && currentTab !== 'favorite') {
                // 模拟组件激活时的数据加载
                loadCount++;
                loadCountElement.textContent = loadCount;
                
                log('SessionFavorite组件激活，开始加载收藏数据', 'info');
                
                // 模拟API调用
                setTimeout(() => {
                    log('模拟API调用: getFavoriteSessionList()', 'info');
                    setTimeout(() => {
                        log('收藏数据加载完成', 'success');
                    }, 500);
                }, 200);
            }
            
            activeStatus.textContent = active;
            currentTab = active ? 'favorite' : 'query';
        }
        
        // 日志函数
        function log(message, type = 'info') {
            const logContent = document.getElementById('log-content');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `
                <span class="timestamp">[${timestamp}]</span> ${message}
            `;
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('log-content').innerHTML = '';
        }
        
        // Tab切换逻辑
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                
                // 更新Tab状态
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                tab.classList.add('active');
                document.getElementById(`${tabName}-content`).classList.add('active');
                
                // 记录Tab切换
                log(`切换到 ${tabName === 'query' ? '会话查询' : '会话收藏'} Tab`, 'info');
                
                // 模拟SessionFavorite组件的active prop变化
                if (tabName === 'favorite') {
                    simulateSessionFavorite(true);
                } else {
                    simulateSessionFavorite(false);
                }
            });
        });
        
        // 初始化日志
        log('页面加载完成，当前在会话查询Tab', 'info');
        log('点击"会话收藏"Tab来测试数据加载行为', 'warning');
    </script>
</body>
</html>
