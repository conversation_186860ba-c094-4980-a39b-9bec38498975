<template>
  <div class="scene-select">
    <a-select
      v-model:value="selectedValue"
      :placeholder="placeholder"
      :loading="loading"
      :disabled="disabled"
      @change="handleChange"
      allow-clear
    >
      <a-select-option v-for="option in options" :key="option.value" :value="option.value">
        {{ option.label }}
      </a-select-option>
    </a-select>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';

export default defineComponent({
  name: 'SceneSelect',
  props: {
    value: {
      type: [String, Number, null],
      default: null
    },
    formData: {
      type: Object,
      required: true
    },
    sceneRelationConfig: {
      type: Object,
      required: true
    },
    placeholder: {
      type: String,
      default: '请选择场景'
    }
  },
  emits: ['update:value', 'change'],
  setup(props, { emit }) {
    const loading = ref(false);
    const options = ref([]);
    const selectedValue = ref(null);
    
    // 是否禁用
    const disabled = computed(() => false);
    
    // 获取场景选项
    const fetchSceneOptions = async () => {
      if (disabled.value) {
        options.value = [];
        return;
      }
      
      const workspaceAndApp = props.formData.workspaceAndApp;
      const applicationVersionId = props.formData.applicationVersionId;
      
      if (!workspaceAndApp || workspaceAndApp.length < 2 || !applicationVersionId) {
        options.value = [];
        return;
      }
      
      loading.value = true;
      try {
        console.log('SceneSelect组件接收到的配置:', props.sceneRelationConfig);
        
        // 适配原始应用API返回的数据结构
        // 原始应用返回格式: { sceneInfos: [{ sceneId, sceneName, workspaceAppList: [{ workspaceId, workspaceName, applicationList: [{ applicationId, applicationName }] }] }] }
        const config = props.sceneRelationConfig;
        if (!config || !config.sceneInfos) {
          options.value = [];
          return;
        }
        
        // 查找与当前选中空间和应用匹配的场景
        const sceneOptions = [];
        
        config.sceneInfos.forEach(scene => {
          if (scene.workspaceAppList && Array.isArray(scene.workspaceAppList)) {
            // 检查这个场景是否包含选中的空间和应用
            const matchingWorkspaceApp = scene.workspaceAppList.find(wa => 
              wa.workspaceId === workspaceAndApp[0] &&
              wa.applicationList && 
              wa.applicationList.some(app => app.applicationId === workspaceAndApp[1])
            );
            
            if (matchingWorkspaceApp) {
              sceneOptions.push({
                value: scene.sceneId.toString(),
                label: scene.sceneName || `场景 ${scene.sceneId}`
              });
            }
          }
        });
        
        options.value = sceneOptions;
      } catch (error) {
        console.error('获取场景失败', error);
        message.error('获取场景失败');
        options.value = [];
      } finally {
        loading.value = false;
      }
    };
    
    // 监听应用版本变化
    watch(() => props.formData.applicationVersionId, () => {
      // 清空已选值
      selectedValue.value = null;
      emit('update:value', null);
      emit('change', null);
      
      fetchSceneOptions();
    });
    
    // 监听场景关系配置变化
    watch(() => props.sceneRelationConfig, () => {
      fetchSceneOptions();
    }, { deep: true });
    
    // 监听外部值变化
    watch(() => props.value, (newVal) => {
      if (newVal !== selectedValue.value) {
        selectedValue.value = newVal;
      }
    });
    
    // 处理值变化
    const handleChange = (value) => {
      emit('update:value', value);
      emit('change', value); // 父组件需监听此事件并据sceneInfos更新空间/应用options
    };
    
    return {
      loading,
      options,
      selectedValue,
      disabled,
      handleChange
    };
  }
});
</script>

<style scoped>
.scene-select {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}
</style> 