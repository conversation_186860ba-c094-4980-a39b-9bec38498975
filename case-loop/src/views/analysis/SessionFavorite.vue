<template>
  <div class="session-favorite">
    <!-- 收藏会话列表 -->
    <a-card :bordered="false">
      <template #title>
        <div class="table-header">
          <div class="table-title">收藏的会话</div>
          <div class="table-actions">
            <a-space>
              <a-button
                  :disabled="!hasFavoriteSelectedRows"
                  @click="handleRemoveFavoriteClick"
                  danger
                  class="custom-button"
              >
                <template #icon>
                  <DeleteOutlined/>
                </template>
                移除收藏
              </a-button>
              <a-button
                  :disabled="!hasFavoriteSelectedRows"
                  @click="handleExportFavoriteClick"
                  class="custom-button"
              >
                <template #icon>
                  <ExportOutlined/>
                </template>
                导出选中
              </a-button>
            </a-space>
          </div>
        </div>
      </template>

      <a-table
          :columns="favoriteColumns"
          :data-source="favoriteTableData"
          :loading="favoriteLoading"
          :pagination="favoritePagination"
          :row-selection="{ selectedRowKeys: favoriteSelectedRowKeys, onChange: onFavoriteSelectChange }"
          :row-key="record => record.sessionId"
          @change="handleFavoriteTableChange"
      >
        <!-- 会话ID列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'sessionId'">
            <a @click="viewSessionDetail(record)">{{ record.sessionId }}</a>
          </template>

          <!-- 空间/应用列 -->
          <template v-if="column.key === 'workspaceApp'">
            <div v-if="record.workspaceAppInfo && record.workspaceAppInfo.length > 0">
              <a-tag color="blue" v-for="ws in record.workspaceAppInfo" :key="ws.workspaceId">
                {{ ws.workspaceName }}
              </a-tag>
              <a-tag color="green" v-for="app in record.workspaceAppInfo.flatMap(ws => ws.applicationList)"
                     :key="app.applicationId">
                {{ app.applicationName }}
              </a-tag>
            </div>
            <span v-else>-</span>
          </template>

          <!-- 场景列 -->
          <template v-if="column.dataIndex === 'sceneName'">
            <a-tag v-if="record.sceneName" :color="getScenarioColor(record.sceneName)">
              {{ record.sceneName }}
            </a-tag>
            <span v-else>-</span>
          </template>

          <!-- 转人工列 -->
          <template v-if="column.dataIndex === 'transferStaff'">
            <template v-if="record.transferStaff !== null && record.transferStaff !== undefined">
              <a-tag :color="getTransferStaffTagColor(record.transferStaff)">
                {{ getTransferStaffTagText(record.transferStaff) }}
              </a-tag>
            </template>
            <span v-else>-</span>
          </template>

          <!-- 解决情况列 -->
          <template v-if="column.dataIndex === 'sessionSolved'">
            <template v-if="record.sessionSolved !== null && record.sessionSolved !== undefined">
              <a-tag :color="getSolvedTagColor(record.sessionSolved)">
                {{ getSolvedTagText(record.sessionSolved) }}
              </a-tag>
            </template>
            <span v-else>-</span>
          </template>

          <!-- 满意度列 -->
          <template v-if="column.dataIndex === 'stars'">
            <div v-if="record.stars !== null && record.stars !== undefined">
              <a-rate :value="record.stars" disabled :count="5"/>
            </div>
            <span v-else>-</span>
          </template>

          <!-- 收藏时间列 -->
          <template v-if="column.dataIndex === 'favoriteTime'">
            {{ record.favoriteTime ? new Date(record.favoriteTime).toLocaleString() : '-' }}
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a @click="viewSessionDetail(record)">查看</a>
              <a @click="removeSingleFavorite(record)" style="color: #ff4d4f;">移除收藏</a>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import {defineComponent, ref, reactive, computed, onMounted, watch} from 'vue';
import {message} from 'ant-design-vue';
import {
  ExportOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import {useRouter} from 'vue-router';

// 导入API
import {
  getFavoriteSessionList,
  unfavoriteSession
} from '@/api/analysis';

export default defineComponent({
  name: 'SessionFavorite',
  components: {
    ExportOutlined,
    DeleteOutlined
  },
  props: {
    active: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const router = useRouter();

    // 收藏会话相关状态
    const favoriteLoading = ref(false);
    const favoriteTableData = ref([]);
    const favoriteSelectedRowKeys = ref([]);
    const hasFavoriteSelectedRows = computed(() => favoriteSelectedRowKeys.value.length > 0);

    // 收藏会话分页配置
    const favoritePagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true
    });

    // 收藏会话表格列配置
    const favoriteColumns = [
      {
        title: '会话ID',
        dataIndex: 'sessionId',
        key: 'sessionId',
        width: 200,
        fixed: 'left'
      },
      {
        title: '创建时间',
        dataIndex: 'time',
        key: 'time',
        width: 170,
        sorter: true,
        customRender: ({text}) => {
          return text ? new Date(text).toLocaleString() : '-';
        }
      },
      {
        title: '空间/应用',
        key: 'workspaceApp',
        width: 150,
        customRender: ({record}) => {
          if (record.workspaceAppInfo && record.workspaceAppInfo.length > 0) {
            const workspaceNames = record.workspaceAppInfo.map(ws => ws.workspaceName);
            const appNames = record.workspaceAppInfo.flatMap(ws =>
                ws.applicationList.map(app => app.applicationName)
            );
            return `${workspaceNames.join(', ')}/${appNames.join(', ')}`;
          }
          return '-';
        }
      },
      {
        title: '场景',
        dataIndex: 'sceneName',
        key: 'sceneName',
        width: 150
      },
      {
        title: '转人工',
        dataIndex: 'transferStaff',
        key: 'transferStaff',
        width: 100
      },
      {
        title: '是否解决',
        dataIndex: 'sessionSolved',
        key: 'sessionSolved',
        width: 100
      },
      {
        title: '满意度',
        dataIndex: 'stars',
        key: 'stars',
        width: 150
      },
      {
        title: '收藏时间',
        dataIndex: 'favoriteTime',
        key: 'favoriteTime',
        width: 170,
        sorter: true
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 150,
        fixed: 'right'
      }
    ];

    // 转换满意度值为数字
    const convertStarsToNumber = (stars) => {
      // 如果是null或undefined，直接返回null
      if (stars === null || stars === undefined) {
        return null;
      }

      // 如果已经是数字且在有效范围内，直接返回
      if (typeof stars === 'number' && !isNaN(stars) && stars >= 0 && stars <= 5) {
        return stars;
      }

      // 如果是字符串，尝试处理
      if (typeof stars === 'string') {
        // 去除前后空格
        const trimmedStars = stars.trim();

        // 如果是空字符串或特殊文本，返回null
        if (trimmedStars === '' || trimmedStars === '未评价' || trimmedStars === 'N/A' || trimmedStars === '-') {
          return null;
        }

        // 尝试转换为数字
        const numValue = parseFloat(trimmedStars);
        if (!isNaN(numValue) && numValue >= 0 && numValue <= 5) {
          return numValue;
        }
      }

      // 其他情况返回null
      return null;
    };

    // 获取收藏会话数据
    const fetchFavoriteData = async () => {
      favoriteLoading.value = true;
      try {
        // 构建请求参数
        const params = {
          pageNum: favoritePagination.current,
          pageSize: favoritePagination.pageSize,
          channel: 'online'
        };

        const res = await getFavoriteSessionList(params);
        console.log('收藏会话API返回数据:', res);

        // 检查API返回状态
        if (res.code === 0) {
          let responseData = [];
          let total = 0;

          // 处理不同的返回格式
          if (Array.isArray(res.data)) {
            responseData = res.data;
            total = responseData.length;
          } else if (res.data && Array.isArray(res.data.data)) {
            responseData = res.data.data;
            total = res.data.totalNum || responseData.length;
          } else if (res.data && Array.isArray(res.data.list)) {
            responseData = res.data.list;
            total = res.data.total || responseData.length;
          } else {
            responseData = [];
            total = 0;
          }

          console.log('处理后的收藏数据:', responseData);

          // 处理数据，确保字段正确映射
          favoriteTableData.value = responseData.map(item => ({
            ...item,
            sessionSolved: item.sessionSolved || null,
            stars: convertStarsToNumber(item.stars),
            transferStaff: item.transferStaff || null,
            favoriteTime: item.favoriteTime || item.createTime || null
          }));

          favoritePagination.total = total;

          console.log('最终表格数据:', favoriteTableData.value);
          console.log('分页信息:', favoritePagination);

          if (responseData.length === 0) {
            message.info('暂无收藏的会话');
          } else {
            message.success(`成功加载 ${responseData.length} 条收藏记录`);
          }
        } else {
          console.error('API返回错误:', res);
          message.error(res.message || '获取收藏会话失败');
          favoriteTableData.value = [];
          favoritePagination.total = 0;
        }
      } catch (error) {
        console.error('获取收藏会话失败', error);
        message.error('获取收藏会话失败');
        favoriteTableData.value = [];
        favoritePagination.total = 0;
      } finally {
        favoriteLoading.value = false;
      }
    };

    // 处理收藏会话表格变更
    const handleFavoriteTableChange = (pag, filters, sorter) => {
      // 处理分页
      favoritePagination.current = pag.current;
      favoritePagination.pageSize = pag.pageSize;

      // 收藏会话列表通常不需要重新请求，因为数据量不大
      // 如果需要服务端分页，可以在这里添加重新请求的逻辑
    };

    // 处理收藏会话选择变更
    const onFavoriteSelectChange = (selectedRowKeys) => {
      favoriteSelectedRowKeys.value = selectedRowKeys;
    };

    // 移除单个收藏
    const removeSingleFavorite = async (record) => {
      try {
        const res = await unfavoriteSession(record.sessionId);
        if (res.code === 0) {
          message.success('已移除收藏');
          fetchFavoriteData(); // 重新获取收藏列表
        } else {
          message.error(res.message || '移除收藏失败');
        }
      } catch (error) {
        console.error('移除收藏失败', error);
        message.error('移除收藏失败');
      }
    };

    // 批量移除收藏
    const handleRemoveFavoriteClick = async () => {
      if (!hasFavoriteSelectedRows.value) {
        message.warning('请先选择要移除的收藏');
        return;
      }

      try {
        // 批量移除收藏
        const promises = favoriteSelectedRowKeys.value.map(sessionId =>
          unfavoriteSession(sessionId)
        );

        const results = await Promise.all(promises);
        const successCount = results.filter(res => res.code === 0).length;

        if (successCount === favoriteSelectedRowKeys.value.length) {
          message.success(`成功移除 ${successCount} 个收藏`);
        } else {
          message.warning(`成功移除 ${successCount} 个收藏，${favoriteSelectedRowKeys.value.length - successCount} 个失败`);
        }

        favoriteSelectedRowKeys.value = [];
        fetchFavoriteData(); // 重新获取收藏列表
      } catch (error) {
        console.error('批量移除收藏失败', error);
        message.error('批量移除收藏失败');
      }
    };

    // 导出收藏会话
    const handleExportFavoriteClick = () => {
      if (!hasFavoriteSelectedRows.value) {
        message.warning('请先选择要导出的收藏');
        return;
      }

      // 导出收藏会话的逻辑
      // TODO: 实现导出功能
      message.success(`成功导出 ${favoriteSelectedRowKeys.value.length} 条收藏记录`);
    };

    // 获取解决标签颜色
    const getSolvedTagColor = (solved) => {
      if (solved === null || solved === undefined) return '';

      // 处理不同类型的解决状态
      if (typeof solved === 'boolean') {
        return solved ? 'success' : 'error';
      }

      if (typeof solved === 'string') {
        // 处理字符串类型
        const mapping = {
          'Y': 'success',
          'N': 'error',
          'U': '',
          'true': 'success',
          'false': 'error',
          '是': 'success',
          '否': 'error',
          '已解决': 'success',
          '未解决': 'error',
          '未评价': ''
        };

        return mapping[solved] || '';
      }

      // 默认情况
      return '';
    };

    // 获取解决标签文本
    const getSolvedTagText = (solved) => {
      if (solved === null || solved === undefined) return '-';

      // 处理不同类型的解决状态
      if (typeof solved === 'boolean') {
        return solved ? '已解决' : '未解决';
      }

      if (typeof solved === 'string') {
        // 处理字符串类型
        if (solved === 'Y' || solved === 'true' || solved === '是') {
          return '已解决';
        }

        if (solved === 'N' || solved === 'false' || solved === '否') {
          return '未解决';
        }

        if (solved === 'U') {
          return '未评价';
        }

        // 如果已经是完整的文本，直接返回
        if (solved === '已解决' || solved === '未解决' || solved === '未评价') {
          return solved;
        }

        // 如果是其他字符串，直接返回
        return solved;
      }

      // 默认情况
      return '-';
    };

    // 获取场景颜色
    const getScenarioColor = (sceneName) => {
      // 根据场景名称返回不同的颜色
      // 这里使用一些常见场景的映射，可以根据实际需求调整
      const colorMap = {
        '外客在线智能': 'blue',
        '骑手在线智能': 'green',
        '拼好饭在线智能': 'orange',
        '拼好饭在线虚拟客服': 'purple',
        '外商在线智能': 'cyan',
        '外客直出': 'magenta',
        '外客履约': 'blue',
        '外客售后': 'orange',
        '外客全场景': 'green'
      };

      // 如果没有匹配的场景，返回默认颜色
      return colorMap[sceneName] || 'blue';
    };

    // 获取转人工标签颜色
    const getTransferStaffTagColor = (transferStaff) => {
      if (transferStaff === null || transferStaff === undefined) return '';

      // 处理不同类型的转人工状态
      if (typeof transferStaff === 'boolean') {
        return transferStaff ? 'blue' : 'green';
      }

      if (typeof transferStaff === 'string') {
        // 处理字符串类型
        const mapping = {
          'Y': 'blue',
          'N': 'green',
          'true': 'blue',
          'false': 'green',
          '是': 'blue',
          '否': 'green',
          '已转人工': 'blue',
          '未转人工': 'green'
        };

        return mapping[transferStaff] || 'blue';
      }

      // 默认情况
      return 'blue';
    };

    // 获取转人工标签文本
    const getTransferStaffTagText = (transferStaff) => {
      if (transferStaff === null || transferStaff === undefined) return '-';

      // 处理不同类型的转人工状态
      if (typeof transferStaff === 'boolean') {
        return transferStaff ? '已转人工' : '未转人工';
      }

      if (typeof transferStaff === 'string') {
        // 处理字符串类型
        if (transferStaff === 'Y' || transferStaff === 'true' || transferStaff === '是') {
          return '已转人工';
        }

        if (transferStaff === 'N' || transferStaff === 'false' || transferStaff === '否') {
          return '未转人工';
        }

        // 如果已经是完整的文本，直接返回
        if (transferStaff === '已转人工' || transferStaff === '未转人工') {
          return transferStaff;
        }

        // 如果是其他字符串，直接返回
        return transferStaff;
      }

      // 默认情况
      return '已转人工';
    };

    // 查看会话详情
    const viewSessionDetail = (record) => {
      router.push({
        name: 'SessionDetail',
        params: {sessionId: record.sessionId}
      });
    };

    // 监听active prop变化
    watch(() => props.active, (newActive, oldActive) => {
      // 当组件从非激活状态变为激活状态时，加载数据
      if (newActive && !oldActive) {
        console.log('SessionFavorite组件激活，开始加载收藏数据');
        fetchFavoriteData();
      }
    }, { immediate: false });

    // 初始化
    onMounted(() => {
      // 如果组件初始化时就是激活状态，则立即加载数据
      if (props.active) {
        fetchFavoriteData();
      }
    });

    return {
      // 状态
      favoriteLoading,
      favoriteTableData,
      favoriteSelectedRowKeys,
      hasFavoriteSelectedRows,
      favoritePagination,
      favoriteColumns,

      // 方法
      fetchFavoriteData,
      handleFavoriteTableChange,
      onFavoriteSelectChange,
      removeSingleFavorite,
      handleRemoveFavoriteClick,
      handleExportFavoriteClick,
      getSolvedTagColor,
      getSolvedTagText,
      getScenarioColor,
      getTransferStaffTagColor,
      getTransferStaffTagText,
      viewSessionDetail
    };
  }
});
</script>

<style scoped>
.session-favorite {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.table-title {
  font-size: 16px;
  font-weight: 500;
}

.table-actions {
  display: flex;
  align-items: center;
}

.custom-button {
  border-radius: 8px;
  transition: all 0.3s;
  margin-right: 8px;
}

.custom-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

:deep(.ant-table-column-title) {
  font-weight: 500;
}
</style>
