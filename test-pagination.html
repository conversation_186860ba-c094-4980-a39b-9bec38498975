<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .pagination-demo {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
            background: #fafafa;
        }
        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        .pagination-controls button {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .pagination-controls button:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        .pagination-controls button.active {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        .pagination-controls button:disabled {
            background: #f5f5f5;
            color: #bfbfbf;
            cursor: not-allowed;
        }
        .pagination-controls select {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        .status-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 20px;
        }
        .log {
            background: #f6f6f6;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 12px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 8px;
            padding: 4px 8px;
            border-radius: 3px;
        }
        .log-info {
            background: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        .log-success {
            background: #f6ffed;
            border-left: 3px solid #52c41a;
        }
        .log-warning {
            background: #fffbe6;
            border-left: 3px solid #faad14;
        }
        .timestamp {
            color: #666;
            font-size: 12px;
        }
        .clear-btn {
            background: #ff4d4f;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        .clear-btn:hover {
            background: #ff7875;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #e8e8e8;
            padding: 8px 12px;
            text-align: left;
        }
        .data-table th {
            background: #fafafa;
            font-weight: 500;
        }
        .data-table tr:nth-child(even) {
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>会话收藏分页功能测试</h1>
        <p>这个测试模拟了SessionFavorite组件中的分页行为，验证分页切换时是否会正确发送API请求。</p>
        
        <div class="status-info">
            <h3>当前状态</h3>
            <p><strong>当前页码：</strong><span id="current-page">1</span></p>
            <p><strong>每页条数：</strong><span id="page-size">10</span></p>
            <p><strong>总记录数：</strong><span id="total-records">50</span></p>
            <p><strong>API调用次数：</strong><span id="api-call-count">0</span></p>
        </div>

        <div class="pagination-demo">
            <h3>分页控件模拟</h3>
            
            <div class="pagination-controls">
                <button id="prev-btn" onclick="changePage('prev')">上一页</button>
                <button class="page-btn active" onclick="changePage(1)">1</button>
                <button class="page-btn" onclick="changePage(2)">2</button>
                <button class="page-btn" onclick="changePage(3)">3</button>
                <button class="page-btn" onclick="changePage(4)">4</button>
                <button class="page-btn" onclick="changePage(5)">5</button>
                <button id="next-btn" onclick="changePage('next')">下一页</button>
                
                <span style="margin-left: 20px;">每页</span>
                <select id="page-size-select" onchange="changePageSize(this.value)">
                    <option value="10">10 条</option>
                    <option value="20">20 条</option>
                    <option value="50">50 条</option>
                </select>
            </div>

            <div>
                <strong>模拟数据：</strong>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>会话ID</th>
                            <th>创建时间</th>
                            <th>场景</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="data-tbody">
                        <!-- 数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="log">
            <button class="clear-btn" onclick="clearLog()">清空日志</button>
            <div id="log-content"></div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let pageSize = 10;
        let totalRecords = 50;
        let apiCallCount = 0;
        
        // 模拟API调用
        function simulateAPICall(page, size) {
            apiCallCount++;
            document.getElementById('api-call-count').textContent = apiCallCount;
            
            log(`API调用: getFavoriteSessionList({ pageNum: ${page}, pageSize: ${size}, channel: 'online' })`, 'info');
            
            // 模拟API响应延迟
            setTimeout(() => {
                const startIndex = (page - 1) * size;
                const endIndex = Math.min(startIndex + size, totalRecords);
                const recordCount = endIndex - startIndex;
                
                log(`API响应: 成功返回第 ${startIndex + 1}-${endIndex} 条记录，共 ${recordCount} 条`, 'success');
                
                // 更新表格数据
                updateTableData(page, size);
            }, 300);
        }
        
        // 更新表格数据
        function updateTableData(page, size) {
            const tbody = document.getElementById('data-tbody');
            tbody.innerHTML = '';
            
            const startIndex = (page - 1) * size;
            const endIndex = Math.min(startIndex + size, totalRecords);
            
            for (let i = startIndex; i < endIndex; i++) {
                const row = tbody.insertRow();
                row.insertCell(0).textContent = `SESSION_${String(i + 1).padStart(6, '0')}`;
                row.insertCell(1).textContent = new Date(Date.now() - i * 3600000).toLocaleString();
                row.insertCell(2).textContent = ['外客在线智能', '骑手在线智能', '拼好饭在线智能'][i % 3];
                row.insertCell(3).textContent = ['已解决', '未解决', '未评价'][i % 3];
            }
        }
        
        // 切换页码
        function changePage(page) {
            let newPage = currentPage;
            
            if (page === 'prev') {
                newPage = Math.max(1, currentPage - 1);
            } else if (page === 'next') {
                newPage = Math.min(Math.ceil(totalRecords / pageSize), currentPage + 1);
            } else {
                newPage = parseInt(page);
            }
            
            if (newPage !== currentPage) {
                log(`分页变更：从第 ${currentPage} 页切换到第 ${newPage} 页`, 'warning');
                
                currentPage = newPage;
                updatePageStatus();
                updatePageButtons();
                
                // 模拟SessionFavorite组件的handleFavoriteTableChange逻辑
                simulateAPICall(currentPage, pageSize);
            }
        }
        
        // 切换每页条数
        function changePageSize(newSize) {
            const oldSize = pageSize;
            pageSize = parseInt(newSize);
            
            log(`每页条数变更：从 ${oldSize} 条变更为 ${pageSize} 条`, 'warning');
            
            // 重新计算当前页码，确保不超出范围
            currentPage = 1;
            updatePageStatus();
            updatePageButtons();
            
            // 模拟API调用
            simulateAPICall(currentPage, pageSize);
        }
        
        // 更新页面状态显示
        function updatePageStatus() {
            document.getElementById('current-page').textContent = currentPage;
            document.getElementById('page-size').textContent = pageSize;
            document.getElementById('total-records').textContent = totalRecords;
        }
        
        // 更新分页按钮状态
        function updatePageButtons() {
            const pageButtons = document.querySelectorAll('.page-btn');
            pageButtons.forEach((btn, index) => {
                btn.classList.toggle('active', index + 1 === currentPage);
            });
            
            document.getElementById('prev-btn').disabled = currentPage === 1;
            document.getElementById('next-btn').disabled = currentPage >= Math.ceil(totalRecords / pageSize);
        }
        
        // 日志函数
        function log(message, type = 'info') {
            const logContent = document.getElementById('log-content');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `
                <span class="timestamp">[${timestamp}]</span> ${message}
            `;
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('log-content').innerHTML = '';
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            updatePageStatus();
            updatePageButtons();
            updateTableData(currentPage, pageSize);
            
            log('分页测试页面初始化完成', 'info');
            log('点击分页按钮或切换每页条数来测试API调用', 'warning');
        });
    </script>
</body>
</html>
