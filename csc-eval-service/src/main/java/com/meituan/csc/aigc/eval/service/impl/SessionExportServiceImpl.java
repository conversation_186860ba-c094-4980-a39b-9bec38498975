package com.meituan.csc.aigc.eval.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.dao.entity.AidaMessagesPo;
import com.meituan.csc.aigc.eval.dao.entity.MetricConfigPo;
import com.meituan.csc.aigc.eval.dao.entity.WorkbenchInspectInfoPo;
import com.meituan.csc.aigc.eval.dao.service.generator.AidaMessagesGeneratorService;
import com.meituan.csc.aigc.eval.dao.service.generator.MetricConfigGeneratorService;
import com.meituan.csc.aigc.eval.dao.service.generator.WorkbenchInspectInfoGeneratorService;
import com.meituan.csc.aigc.eval.dto.workbench.InspectWorkbenchQueryDetailDTO;
import com.meituan.csc.aigc.eval.dto.workbench.InspectWorkbenchSessionDetailDTO;
import com.meituan.csc.aigc.eval.dto.workbench.SessionExportDTO;
import com.meituan.csc.aigc.eval.enums.DimensionTypeEnum;
import com.meituan.csc.aigc.eval.enums.MetricEvalTypeEnum;
import com.meituan.csc.aigc.eval.enums.PlatformTypeEnum;
import com.meituan.csc.aigc.eval.enums.RoleEnum;
import com.meituan.csc.aigc.eval.enums.workbench.SessionDataExportEnum;
import com.meituan.csc.aigc.eval.helper.ConversationThreadLocalHelper;
import com.meituan.csc.aigc.eval.param.inspect.InspectWorkbenchDetailParam;
import com.meituan.csc.aigc.eval.proxy.AidaInvokeServiceProxy;
import com.meituan.csc.aigc.eval.service.ISessionExportService;
import com.meituan.csc.aigc.eval.service.InspectWorkbenchExecuteService;
import com.meituan.csc.aigc.eval.service.helper.OperationAnalysisHelper;
import com.meituan.csc.aigc.eval.utils.DateUtil;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.sankuai.csccratos.aida.config.client.dto.eval.AppDTO;
import com.sankuai.csccratos.aida.config.client.dto.eval.TenantDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: session导出服务实现
 * Date: 2025/3/19 12:12
 * Author: libin111
 */
@Service
public class SessionExportServiceImpl implements ISessionExportService {

    /**
     * 日志
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(SessionExportServiceImpl.class);

    /**
     * 人工质检格式 (指标名称-质检人-{列名})
     */
    private static final String METRIC_MANUAL_FORMAT = "%s-%s-%s";

    /**
     * 评测结果
     */
    private static final String EVALUATION_RESULT = "评测结果";

    /**
     * 评测时间
     */
    private static final String EVALUATION_TIME = "评测时间";

    /**
     * 评测备注
     */
    private static final String EVALUATION_NOTE = "评测备注";

    /**
     * 状态信息 - 已质检
     */
    private static final String STATUS_EVALUATED = "已质检";

    /**
     * 状态信息 - 未质检
     */
    private static final String STATUS_NOT_EVALUATED = "未质检";

    /**
     * 输入
     */
    private static final String INPUT_DISPLAY_NAME = "输入";

    /**
     * 输出
     */
    private static final String OUTPUT_DISPLAY_NAME = "输出";

    /**
     * 质检详情服务
     */
    @Autowired
    private WorkbenchInspectInfoGeneratorService workbenchInspectInfoGeneratorService;

    /**
     * 质检配置信息服务
     */
    @Autowired
    private MetricConfigGeneratorService metricConfigGeneratorService;

    /**
     * aida消息处理服务
     */
    @Autowired
    private AidaMessagesGeneratorService aidaMessagesGeneratorService;

    /**
     * aida原生侧服务
     */
    @Autowired
    private AidaInvokeServiceProxy aidaInvokeServiceProxy;

    /**
     * 运营分析业务帮助类服务
     */
    @Autowired
    private OperationAnalysisHelper operationAnalysisHelper;

    /**
     * 质检相关服务
     */
    @Autowired
    private InspectWorkbenchExecuteService inspectWorkbenchExecuteService;

    /**
     * excel相关服务
     */
    @Autowired
    private ExcelService excelService;

    @Override
    public String exportSession(String mis, Set<String> sessionIdSet) {
        // 最终结果
        String result = null;
        // 导出开始时间戳
        long startTime = System.currentTimeMillis();
        LOGGER.info(String.format("session导出开始,mis: %s, sessionIdSet: %s, 开始时间: %s", mis, sessionIdSet, startTime));
        try {
            boolean valid = paramCheckValid(mis, sessionIdSet);
            if (!valid) {
                return null;
            }
            return exportSessionProcess(mis, sessionIdSet);
        } catch (Exception e) {
            LOGGER.error(String.format("session导出失败,mis: %s, sessionIdSet: %s, 失败原因: %s", mis, sessionIdSet, e.getMessage()), e);
            return null;
        } finally {
            LOGGER.info(String.format("session导出结束,mis: %s, sessionIdSet: %s, 结果: %s, 结束时间: %s, 耗时: %s", mis, sessionIdSet, result, System.currentTimeMillis(), System.currentTimeMillis() - startTime));
        }
    }

    /**
     * 会话导出处理
     * 
     * @param mis mis号
     * @param sessionIdSet 会话id集合
     * @return 导出结果
     */
    private String exportSessionProcess(String mis, Set<String> sessionIdSet) {
        // 构建准备结构
        SessionExportDTO sessionExportDTO = prepareSessionExportData(sessionIdSet);
        LOGGER.info(String.format("session导出准备结构: %s", JSON.toJSONString(sessionExportDTO)));

        // 设置表头
        LinkedHashSet<ExcelService.ColorfulHead> excelHeaders = prepareHeaders(sessionExportDTO.getMetricIdAndInspectorSet(), sessionExportDTO.getMetricIdMetricConfigMap());
        LOGGER.info(String.format("session导出表头: %s", JSON.toJSONString(excelHeaders)));

        // 准备数据
        List<List<String>> dataList = prepareExcelRowList(sessionIdSet, excelHeaders, sessionExportDTO);
        LOGGER.info(String.format("session数据导出，数据数量: %s", CollectionUtils.isEmpty(dataList) ? 0 : dataList.size()));

        // 生成并上传数据
        return generateAndUploadExcel(mis, excelHeaders, dataList);
    }

    /**
     * 生成并上传数据
     *
     * @param mis 用户mis
     * @param headList 表头
     * @param dataList 数据
     * @return s3 url
     */
    private String generateAndUploadExcel(String mis, Set<ExcelService.ColorfulHead> headList, List<List<String>> dataList) {

        if (null == dataList) {
            return null;
        }

        String filename = String.format("在线会话分析-数据导出-%s.xlsx",
                DateUtil.format(new Date(), "yyyy_MM_dd_HH_mm_ss"));

        String s3Filename = String.format("%s-%d.xlsx", filename, System.currentTimeMillis());

        String url = excelService.generateSingletonColorfulHeadExcelAndUpload(
                new ArrayList<>(headList),
                dataList,
                "运营分析数据",
                s3Filename,
                filename,
                "xlsx",
                mis
        );

        LOGGER.info(String.format("运营分析工具数据导出, 生成文件并上传成功, mis: %s, url: %s", mis, url));

        return url;
    }

    /**
     * 根据sessionIdSet准备excel的行数据
     *
     * @param sessionIdSet 会话id集合
     * @param excelHeaders 表头
     * @param sessionExportDTO session导出前置结构
     * @return 所有数据
     */
    private List<List<String>> prepareExcelRowList(Set<String> sessionIdSet, LinkedHashSet<ExcelService.ColorfulHead> excelHeaders, SessionExportDTO sessionExportDTO) {
        if (CollectionUtils.isEmpty(sessionIdSet)) {
            return Lists.newArrayList();
        }
        List<List<String>> result = Lists.newArrayList();

        long startTimeAll = System.currentTimeMillis();

        int i = 0;
        int total = sessionIdSet.size();
        for (String sessionId : sessionIdSet) {
            long startTime = System.currentTimeMillis();
            LOGGER.info(String.format("单个sessionId数据导出开始, index:%s, sessionId:%s, total:%s, startTime:%s", i, sessionId, total, startTime));
            try {
                List<List<String>> subResult = prepareExcelRowListBySessionId(sessionId, excelHeaders, sessionExportDTO);
                if (CollectionUtils.isNotEmpty(subResult)) {
                    result.addAll(subResult);
                }
            } catch (Exception e) {
                LOGGER.error(String.format("单个sessionId数据导出异常, index:%s, sessionId:%s, errorMsg:%s", i, sessionId, e.getMessage()), e);
            } finally {
                long endTime = System.currentTimeMillis();
                LOGGER.info(String.format("单个sessionId数据导出结束, index:%s, sessionId:%s, total:%s, startTime:%s, endTime:%s, duration:%s", i, sessionId, total, startTime, endTime, (endTime - startTime)));
                i = i + 1;
            }
        }

        long endTime = System.currentTimeMillis();

        LOGGER.info(String.format("所有sessionId数据导出结束, index:%s, total:%s, startTime:%s, endTime:%s, duration:%s", i, total, startTimeAll, endTime, (endTime - startTimeAll)));

        return result;
    }

    /**
     * 根据sessionId准备excel的行数据
     *
     * @param sessionId 会话id
     * @param excelHeaders 表头
     * @param sessionExportDTO session导出前置结构
     * @return sessionId维度的行数据
     */
    private List<List<String>> prepareExcelRowListBySessionId(String sessionId, LinkedHashSet<ExcelService.ColorfulHead> excelHeaders, SessionExportDTO sessionExportDTO) {
        if (StringUtils.isBlank(sessionId)) {
            return Lists.newArrayList();
        }
        // 根据sessionId查询所有aida messages
        List<AidaMessagesPo> aidaMessagesList = aidaMessagesGeneratorService.listBySessionId(sessionId);
        // 根据所有的aidaMessagesList消息获取所有sessionId下的conversationId列表
//        List<String> allAidaConversationIdList = aidaMessagesList.stream().filter(Objects::nonNull).map(AidaMessagesPo::getConversationId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        // 获取第一层应用的aida消息
        List<AidaMessagesPo> firstLayerAidaMessagesList = operationAnalysisHelper.getFirstLayerAidaMessagesList(aidaMessagesList);
        firstLayerAidaMessagesList = firstLayerAidaMessagesList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        // 根据第一层应用的aida消息获取user信息
        List<String> conversationIdList = firstLayerAidaMessagesList.stream().map(AidaMessagesPo::getConversationId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(conversationIdList)) {
            return Lists.newArrayList();
        }

//        // 根据allAidaConversationIdList获取aida侧原生消息列表
//        List<MessagesDTO> allAidaOriginMessageList;
//        if (LargeTableManagementUtil.enableNewApiWithAppId(aidaMessagesList.get(0).getAppId())) {
//            allAidaOriginMessageList = inspectWorkbenchExecuteService.listMessageByConvIdsAndMainAppId(allAidaConversationIdList);
//        } else {
//            allAidaOriginMessageList = aidaInvokeServiceProxy.listByConversationIds(allAidaConversationIdList);
//        }
        // 获取用户id
        String userId = firstLayerAidaMessagesList.get(0).getUserId();
        // 获取订单id
        String orderId = firstLayerAidaMessagesList.get(0).getOrderId();
        // 获取session detail详情信息 - removeCache是防止sessionDetail内有使用缓存的情况
        ConversationThreadLocalHelper.removeCache();
        List<InspectWorkbenchSessionDetailDTO> inspectWorkbenchSessionDetailDTOList = inspectWorkbenchExecuteService.sessionDetail(sessionId, PlatformTypeEnum.AI.getCode(), conversationIdList);
        // 根据session detail的详情信息获取excel的行数据
        return prepareExcelRowByInspectWorkbenchSessionDetailList(sessionId, userId, orderId, inspectWorkbenchSessionDetailDTOList, excelHeaders, sessionExportDTO, aidaMessagesList);
    }

    /**
     * 根据session/detail数据构建导出数据的行数据
     *
     * @param sessionId 会话id
     * @param userId 用户id
     * @param orderId 订单id
     * @param inspectWorkbenchSessionDetailDTOList session/detail数据
     * @param excelHeaders 表头
     * @param sessionExportDTO session数据导出的前置数据
     * @param allAidaOriginMessageList sessionId下所有aida原始消息列表
     * @return excel的行数据
     */
    private List<List<String>> prepareExcelRowByInspectWorkbenchSessionDetailList(String sessionId, String userId, String orderId, List<InspectWorkbenchSessionDetailDTO> inspectWorkbenchSessionDetailDTOList, LinkedHashSet<ExcelService.ColorfulHead> excelHeaders, SessionExportDTO sessionExportDTO, List<AidaMessagesPo> allAidaOriginMessageList) {
        if (CollectionUtils.isEmpty(inspectWorkbenchSessionDetailDTOList)) {
            return Lists.newArrayList();
        }

        // 最终返回结果
        List<List<String>> result = Lists.newArrayList();
        // 中间值数据 - session维度公共信息
        Map<String, String> sessionGlobalMap = Maps.newHashMap();
        // 基本填充数据(sessionId、userId、orderId)
        sessionGlobalMap.put(SessionDataExportEnum.SESSION_ID.getField(), sessionId);
        sessionGlobalMap.put(SessionDataExportEnum.USER_ID.getField(), userId);
        sessionGlobalMap.put(SessionDataExportEnum.ORDER_ID.getField(), orderId);
        // 根据sessionId获取质检信息详情
        List<WorkbenchInspectInfoPo> workbenchInspectInfoPoList = workbenchInspectInfoGeneratorService.listBySessionIdsAndDimensionAndLLMMessageIdNotNullAndTypeAndAidaPlatform(Sets.newHashSet(sessionId), DimensionTypeEnum.QUERY.getCode(), MetricEvalTypeEnum.MANUAL.getCode());
        // 构建出messageId(第三方业务消息id和按照其分组的质检详情信息列表的映射表)
        Map<String, List<WorkbenchInspectInfoPo>> messageIdWorkbenchInspectInfoListMap = operationAnalysisHelper.buildWorkbenchInspectInfoPoListToMessageIdLevelInfo(workbenchInspectInfoPoList);

        // 轮次
        int turn = 0;
        // 会话历史
        List<Map<String, String>> history = Lists.newArrayList();
        // 遍历session/detail的详情列表
        for (InspectWorkbenchSessionDetailDTO detailDTO : inspectWorkbenchSessionDetailDTOList) {
            if (null == detailDTO) {
                continue;
            }

            // 当前消息
            Map<String, String> curMessage = new HashMap<>();
            RoleEnum role = RoleEnum.getByCode(detailDTO.getSenderType());
            curMessage.put("role", role == null ? "" : role.getDescription());
            curMessage.put("content", detailDTO.getMessage());

            // 首先判断detailDTO是否满足可以查询query/detail的条件
            boolean canQueryDetail = operationAnalysisHelper.judgeInspectWorkbenchSessionDetailCanQueryDetail(detailDTO);
            if (!canQueryDetail) {
                history.add(curMessage);
                continue;
            }
            // 轮次增加1
            turn = turn + 1;

            List<String> dataList = prepareExcelRowByInspectWorkbenchSessionDetail(messageIdWorkbenchInspectInfoListMap, sessionGlobalMap, history, turn, sessionExportDTO, excelHeaders, allAidaOriginMessageList, detailDTO);
            // 当前消息添加为历史
            history.add(curMessage);
            if (CollectionUtils.isNotEmpty(dataList)) {
                result.add(dataList);
            }
        }

        return result;
    }

    /**
     * 以单个detailDTO结构构建导出数据
     *
     * @param messageIdWorkbenchInspectInfoListMap 第三方业务消息id和按照其分组的质检详情信息列表的映射表
     * @param sessionGlobalMap 基本填充数据
     * @param history 上文历史
     * @param turn 轮次
     * @param sessionExportDTO session导出前置查询信息
     * @param excelHeaders excel表头
     * @param allAidaOriginMessageList sessionId下所有aida原始消息列表
     * @param detailDTO session/detail其中一项
     * @return 一条excel数据
     */
    private List<String> prepareExcelRowByInspectWorkbenchSessionDetail(Map<String, List<WorkbenchInspectInfoPo>> messageIdWorkbenchInspectInfoListMap,
                                                                        Map<String, String> sessionGlobalMap, List<Map<String, String>> history, int turn, SessionExportDTO sessionExportDTO,
                                                                        LinkedHashSet<ExcelService.ColorfulHead> excelHeaders, List<AidaMessagesPo> allAidaOriginMessageList,
                                                                        InspectWorkbenchSessionDetailDTO detailDTO) {
        // 数据映射表 - 用于最终构建数据
        Map<String, String> resultMap = new HashMap<>();
        // 填充基本数据 - sessionId、userId、orderId
        resultMap.putAll(sessionGlobalMap);

        // 外部业务消息id
        String messageId = detailDTO.getMessageId();
        // 质检标准id和质检标准详情映射表
        Map<Long, MetricConfigPo> metricIdMetricConfigMap = sessionExportDTO.getMetricIdMetricConfigMap();
        // messageId维度的质检详情信息列表
        List<WorkbenchInspectInfoPo> workbenchInspectInfoPoList = messageIdWorkbenchInspectInfoListMap.get(messageId);
        // 填充质检信息 - 状态，质检详情信息
        fillInspectInfoToResultMap(resultMap, metricIdMetricConfigMap, workbenchInspectInfoPoList);

        // 填充aida应用和空间信息
        resultMap.put(SessionDataExportEnum.WORKSPACE_NAME.getField(), detailDTO.getMessageLevelAidaAppInfo().getWorkspaceName());
        resultMap.put(SessionDataExportEnum.APPLICATION_NAME.getField(), detailDTO.getMessageLevelAidaAppInfo().getApplicationName());

        // 填充轮次和创建时间时间信息
        resultMap.put(SessionDataExportEnum.CREATE_TIME.getField(), DateUtil.format(detailDTO.getTime(), null));
        resultMap.put(SessionDataExportEnum.TURNS.getField(), String.valueOf(turn));

        // 填充历史上文信息
        resultMap.put(SessionDataExportEnum.HISTORY.getField(), JSONObject.toJSONString(history, SerializerFeature.PrettyFormat));

        // 填充query/detail信息
        InspectWorkbenchDetailParam param = new InspectWorkbenchDetailParam();
        param.setApplicationId(detailDTO.getMessageLevelAidaAppInfo().getApplicationId());
        param.setOriginMessage(detailDTO.getOriginMessage());
        param.setPlatform(PlatformTypeEnum.AI.getCode());
        InspectWorkbenchQueryDetailDTO queryDetail = inspectWorkbenchExecuteService.buildAidaQueryDetailDTO(param, allAidaOriginMessageList, null);
        if (null == queryDetail || null == queryDetail.getDetail() || null == queryDetail.getDetail().getBasicInfo()) {
            return Lists.newArrayList();
        }

        // aida应用id和动作节点的映射表
        Map<String, Map<String, String>> aidaAppIdActionNodeConfigMap = sessionExportDTO.getAidaAppIdActionNodeConfigMap();
        // 填充大模型消息id
        resultMap.put(SessionDataExportEnum.LLM_MESSAGE_ID.getField(), queryDetail.getId());
        // 输入内容
        resultMap.put(SessionDataExportEnum.INPUT_CONTENT.getField(),
                queryDetail.getDetail().getBasicInfo().stream()
                        .filter(x -> INPUT_DISPLAY_NAME.equals(x.getDisplayName()))
                        .findFirst()
                        .orElse(new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo(null, ""))
                        .getDisplayContent());
        // 输出内容
        resultMap.put(SessionDataExportEnum.OUTPUT_CONTENT.getField(),
                queryDetail.getDetail().getBasicInfo().stream()
                        .filter(x -> OUTPUT_DISPLAY_NAME.equals(x.getDisplayName()))
                        .findFirst()
                        .orElse(new InspectWorkbenchQueryDetailDTO.Detail.BasicInfo(null, ""))
                        .getDisplayContent());
        // 信号
        if (CollectionUtils.isNotEmpty(queryDetail.getSignalList())) {
            resultMap.put(SessionDataExportEnum.SIGNAL.getField(),
                    JSONObject.toJSONString(queryDetail.getSignalList().stream()
                            .map(signal -> Collections.singletonMap(signal.getSignalName(), signal.getSignalValue()))
                            .collect(Collectors.toList()), SerializerFeature.PrettyFormat));
        }
        // 调用信息
        resultMap.put(SessionDataExportEnum.CALLS.getField(),
                JSONObject.toJSONString(queryDetail.getDetail().getAidaTraceList().stream()
                        .filter(trace -> {
                            if (aidaAppIdActionNodeConfigMap.containsKey(trace.getApplicationId())) {
                                return aidaAppIdActionNodeConfigMap.get(trace.getApplicationId()).containsKey(trace.getNodeId());
                            }
                            return false;
                        })
                        .map(trace -> Collections.singletonMap(
                                StringUtils.isBlank(aidaAppIdActionNodeConfigMap.get(trace.getApplicationId()).get(trace.getNodeId())) ?
                                        trace.getNodeName() :
                                        aidaAppIdActionNodeConfigMap.get(trace.getApplicationId()).get(trace.getNodeId()),
                                trace.getOutput() == null ? "" : trace.getOutput()
                        ))
                        .collect(Collectors.toList())));

        // 构建数据行
        List<String> row = Lists.newArrayList();
        for (ExcelService.ColorfulHead col : excelHeaders) {
            String value = resultMap.get(col.getHeadName());
            if (StringUtils.isBlank(value)) {
                row.add("");
            } else {
                row.add(value);
            }

        }
        return row;
    }

    /**
     * 向resultMap填充质检信息数据(之间指标数据，是否已质检数据)
     *
     * @param resultMap 构建数据的map
     * @param metricIdMetricConfigMap 质检标准id和质检标准详情映射表
     * @param workbenchInspectInfoPoList messageId维度的质检详情信息列表
     */
    private void fillInspectInfoToResultMap(Map<String, String> resultMap, Map<Long, MetricConfigPo> metricIdMetricConfigMap, List<WorkbenchInspectInfoPo> workbenchInspectInfoPoList) {
        if (CollectionUtils.isEmpty(workbenchInspectInfoPoList)) {
            // 未质检
            resultMap.put(SessionDataExportEnum.STATUS.getField(), STATUS_NOT_EVALUATED);
            return;
        }
        // 已质检
        resultMap.put(SessionDataExportEnum.STATUS.getField(), STATUS_EVALUATED);

        // 填充质检详情数据
        for (WorkbenchInspectInfoPo inspectInfoPo : workbenchInspectInfoPoList) {
            // 质检人
            String inspector = inspectInfoPo.getCreatorMis();
            // 指标id
            Long metricId = inspectInfoPo.getMetricId();
            if (!metricIdMetricConfigMap.containsKey(metricId)) {
                continue;
            }
            // 指标详情结构
            MetricConfigPo metricConfigPo = metricIdMetricConfigMap.get(metricId);
            // 指标name
            String metricName = metricConfigPo.getName();

            resultMap.put(this.metricHeadNameFormat(metricName, inspector, EVALUATION_RESULT), inspectInfoPo.getMetricResult());
            resultMap.put(this.metricHeadNameFormat(metricName, inspector, EVALUATION_TIME), DateUtil.format(inspectInfoPo.getGmtModified(), null));
            resultMap.put(this.metricHeadNameFormat(metricName, inspector, EVALUATION_NOTE), inspectInfoPo.getNote());
        }
    }

    /**
     * 质检信息表头
     *
     * @param metricName 指标名
     * @param inspector 质检人
     * @param headName 列名
     * @return 质检信息表头
     */
    private String metricHeadNameFormat(String metricName, String inspector, String headName) {
        return String.format(METRIC_MANUAL_FORMAT, metricName, inspector, headName);
    }

    /**
     * 设置表头
     *
     * @param metricIdAndInspectorSet 指标id和质检人集合
     * @param metricIdMetricConfigMap 指标id和指标详情的映射表
     * @return 表头
     */
    private LinkedHashSet<ExcelService.ColorfulHead> prepareHeaders(Set<SessionExportDTO.MetricIdAndInspectorDTO> metricIdAndInspectorSet, Map<Long, MetricConfigPo> metricIdMetricConfigMap) {
        // 获取基础表头
        LinkedHashSet<ExcelService.ColorfulHead> baseHeaders = prepareBaseHeaders();
        // 追加质检表头
        if (CollectionUtils.isEmpty(metricIdAndInspectorSet)) {
            return baseHeaders;
        }
        // 对metricIdAndInspectorSet按照metricId排序得到一个列表
        List<SessionExportDTO.MetricIdAndInspectorDTO> sortedList = metricIdAndInspectorSet.stream()
                .sorted(Comparator.comparingLong(SessionExportDTO.MetricIdAndInspectorDTO::getMetricId))
                .collect(Collectors.toList());

        // 添加质检指标表头
        addMetricHeadersToBaseHeaders(baseHeaders, sortedList, metricIdMetricConfigMap);

        return baseHeaders;
    }

    /**
     * 对基础表头添加指标表头
     *
     * @param baseHeaders 基础表头
     * @param sortedList 排好序的指标id和质检人的列表
     * @param metricIdMetricConfigMap metricId和metric详情的映射表
     */
    private void addMetricHeadersToBaseHeaders(LinkedHashSet<ExcelService.ColorfulHead> baseHeaders, List<SessionExportDTO.MetricIdAndInspectorDTO> sortedList, Map<Long, MetricConfigPo> metricIdMetricConfigMap) {
        // 新增表头颜色周期循环
        List<IndexedColors> headColors = Arrays.asList(
                IndexedColors.LIGHT_GREEN,
                IndexedColors.PALE_BLUE
        );

        // 遍历该列表，放入到基础表头中
        int colorIndex = 0;
        for (SessionExportDTO.MetricIdAndInspectorDTO metricIdAndInspector : sortedList) {
            // 获取质检指标id
            Long metricId = metricIdAndInspector.getMetricId();
            MetricConfigPo metricConfigPo = metricIdMetricConfigMap.get(metricId);
            if (null == metricConfigPo) {
                continue;
            }
            // 指标名称
            String metricName = metricConfigPo.getName();
            // 质检人
            String inspector = metricIdAndInspector.getInspector();
            // 颜色
            IndexedColors color = headColors.get(colorIndex);

            // 添加评测结果质检列
            baseHeaders.add(new ExcelService.ColorfulHead(this.metricHeadNameFormat(metricName, inspector, EVALUATION_RESULT), null, color));
            // 添加评测时间质检列
            baseHeaders.add(new ExcelService.ColorfulHead(this.metricHeadNameFormat(metricName, inspector, EVALUATION_TIME), null, color));
            // 添加评测备注质检列
            baseHeaders.add(new ExcelService.ColorfulHead(this.metricHeadNameFormat(metricName, inspector, EVALUATION_NOTE), null, color));

            colorIndex = (colorIndex + 1) % headColors.size();
        }
    }

    /**
     * 准备基础表头
     *
     * @return 导出Excel的基础表头集合
     */
    private LinkedHashSet<ExcelService.ColorfulHead> prepareBaseHeaders() {
        return SessionDataExportEnum.listFields().stream()
                .map(head -> new ExcelService.ColorfulHead(
                        head, null, IndexedColors.LIGHT_TURQUOISE
                ))
                .collect(Collectors.toCollection(LinkedHashSet::new));
    }

    /**
     * 准备session数据导出的前置数据
     *
     * @param sessionIdSet 会话id集合
     * @return 前置数据结构
     */
    private SessionExportDTO prepareSessionExportData(Set<String> sessionIdSet) {
        // 创建session导出前置结构
        SessionExportDTO sessionExportDTO = new SessionExportDTO();

        // 根据sessionIdSet获取所有人工质检信息
        List<WorkbenchInspectInfoPo> list = workbenchInspectInfoGeneratorService.listBySessionIdsAndDimensionAndLLMMessageIdNotNullAndTypeAndAidaPlatform(sessionIdSet, DimensionTypeEnum.QUERY.getCode(), MetricEvalTypeEnum.MANUAL.getCode());
        list = list.stream().filter(item -> null != item.getMetricId()).collect(Collectors.toList());
        // 获取人工质检指标id集合
        Set<Long> manualMetricIdSet = list.stream().map(WorkbenchInspectInfoPo::getMetricId).collect(Collectors.toSet());
        // 指标ID到指标详情的映射
        List<MetricConfigPo> metricConfigList = metricConfigGeneratorService.getByIdList(Lists.newArrayList(manualMetricIdSet));
        // 获取人工质检指标id和指标详情的映射
        Map<Long, MetricConfigPo> metricIdMetricConfigMap = metricConfigList.stream()
                .collect(Collectors.toMap(MetricConfigPo::getId, Function.identity()));

        // 根据人工质检信息获取人工质检指标和评测人的集合
        Set<SessionExportDTO.MetricIdAndInspectorDTO> metricIdAndInspectorSet = list.stream()
                .filter(item -> metricIdMetricConfigMap.containsKey(item.getMetricId()))
                .map(item -> {
                    SessionExportDTO.MetricIdAndInspectorDTO dto = new SessionExportDTO.MetricIdAndInspectorDTO();
                    dto.setMetricId(item.getMetricId());
                    dto.setInspector(item.getCreatorMis());
                    return dto;
                })
                .collect(Collectors.toSet());

        // 根据sessionIdSet查询所有aida应用id信息
        List<String> aidaAppIdList = aidaMessagesGeneratorService.listAidaApplicationIdListBySessionIds(Lists.newArrayList(sessionIdSet));
        List<AppDTO> appDtoS = aidaInvokeServiceProxy.listAidaAppDtoByIds(aidaAppIdList);
        Map<String, AppDTO> appIdMap = appDtoS.stream().filter(Objects::nonNull).filter(app -> StringUtils.isNotBlank(app.getId()) && StringUtils.isNotBlank(app.getTenantId()) && StringUtils.isNotBlank(app.getName())).collect(Collectors.toMap(AppDTO::getId, Function.identity()));
        List<String> workspaceIdList = appIdMap.values().stream().map(AppDTO::getTenantId).distinct().collect(Collectors.toList());
        List<TenantDTO> tenantDTOS = aidaInvokeServiceProxy.listAidaTenantDtoByIds(workspaceIdList);
        // 空间id和空间名称的映射表
        Map<String, String> tenantIdNameMap = tenantDTOS.stream().filter(Objects::nonNull).filter(tenant -> StringUtils.isNotBlank(tenant.getId()) && StringUtils.isNotBlank(tenant.getName())).collect(Collectors.toMap(TenantDTO::getId, TenantDTO::getName));
        // aida应用id和aida应用名称的映射表
        Map<String, String> aidaIdNameMap = appIdMap.values().stream().collect(Collectors.toMap(AppDTO::getId, AppDTO::getName));

        // 获取动作配置
        String aidaAppIdActionNodeConfigStr = Lion.getString(ConfigUtil.getAppkey(), LionConstants.AIDA_ACTION_NODE_INFO);
        Map<String, Map<String, String>> aidaAppIdActionNodeConfigMap = JSONObject.parseObject(
                aidaAppIdActionNodeConfigStr, new TypeReference<Map<String, Map<String, String>>>() {
                });

        // 设置处理结果
        sessionExportDTO.setMetricIdMetricConfigMap(metricIdMetricConfigMap);
        sessionExportDTO.setMetricIdAndInspectorSet(metricIdAndInspectorSet);
        sessionExportDTO.setAidaAppIdAndNameMap(aidaIdNameMap);
        sessionExportDTO.setAidaWorkspaceIdAndNameMap(tenantIdNameMap);
        sessionExportDTO.setAidaAppIdActionNodeConfigMap(aidaAppIdActionNodeConfigMap);

        return sessionExportDTO;
    }

    /**
     * 检查参数是否有效
     *
     * @param mis mis号
     * @param sessionIdSet 会话id集合
     * @return 是否有效 true: 有效 false: 无效
     */
    private boolean paramCheckValid(String mis, Set<String> sessionIdSet) {
        return !((StringUtils.isBlank(mis) || CollectionUtils.isEmpty(sessionIdSet)));
    }
}
