package com.meituan.csc.aigc.eval.mq.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.csc.flow.facade.enums.TaskPathEnum;
import com.google.common.collect.Maps;
import com.meituan.csc.aigc.eval.constants.CatConstants;
import com.meituan.csc.aigc.eval.constants.EsScriptConstants;
import com.meituan.csc.aigc.eval.dto.es.AidaOnlineSessionCacheDTO;
import com.meituan.csc.aigc.eval.dto.mq.TaskTraceLog;
import com.meituan.csc.aigc.eval.service.analysis.OnlineSessionRedisService;
import com.meituan.csc.aigc.eval.service.analysis.es.CaseAnalysisEagleService;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * TASK执行记录消费服务，用于更新在线会话ES
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Slf4j
@Service("taskTraceLogListener")
public class TaskTraceLogListener {

    @Autowired
    private OnlineSessionRedisService redisService;

    @Autowired
    private CaseAnalysisEagleService eagleService;

    /**
     * 日志前缀
     */
    private static final String LOG_PREFIX = "[TaskTraceLogListener]";
    /**
     * 异常退出
     */
    private static final String EXCEPTION_EXIT = "EXCEPTION_EXIT";
    /**
     * 超时退出
     */
    private static final String TIME_OUT_EXIT = "TIME_OUT_EXIT";
    /**
     * sessionId
     */
    private static final String SESSION_ID_KEY = "sessionId";
    /**
     * path
     */
    private static final String PATH_KEY = "path";

    @MdpMafkaMsgReceive
    public ConsumeStatus receive(String msgBody) {
        Transaction transaction = Cat.newTransaction(CatConstants.ONLINE_SESSION_MQ_TYPE, CatConstants.ONLINE_SESSION_TASK_LOG);
        try {
            TaskTraceLog traceLog = JSON.parseObject(msgBody, TaskTraceLog.class);
            // sessionId是否为空
            String sessionId = getSessionId(traceLog);
            if (StringUtils.isBlank(sessionId)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 判断是否为首次进入节点
            if (!traceLog.getFirstEnterNode()) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 是否进入AI搭
            AidaOnlineSessionCacheDTO sessionCacheDTO = redisService.getSessionInfo(sessionId);
            if (sessionCacheDTO == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            if (!redisService.getSessionLock(sessionId)) {
                log.error("{}-获取锁失败, sessionId = {}, traceLog = {}", LOG_PREFIX, sessionId, JSON.toJSONString(traceLog));
                return ConsumeStatus.CONSUME_FAILURE;
            }
            // 保存Task执行信息到ES
            try {
                saveTaskInfo(sessionId, traceLog, sessionCacheDTO);
            } catch (Exception e) {
                log.error("{}-更新会话信息异常, sessionId = {}, e:", LOG_PREFIX, sessionId, e);
                transaction.setStatus(e);
            } finally {
                // 删除锁
                redisService.deleteSessionLock(sessionId);
            }
        } catch (Exception e) {
            log.error("{}-消费失败, msgBody = {}", LOG_PREFIX, msgBody, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        // 消费成功
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 解析sessionId
     *
     * @param traceLog task执行记录
     * @return sessionId
     */
    private String getSessionId(TaskTraceLog traceLog) {
        String sessionId = traceLog.getSessionId();
        try {
            Map<String, Object> userSlots = JSON.parseObject(traceLog.getUserSlots(), new TypeReference<Map<String, Object>>() {
            });
            // 如果是轻交互task的traceLog，从userSlots中拿到门户sessionId
            if (MapUtils.isNotEmpty(userSlots) && TaskPathEnum.SELF_HELP.getCode().equals(String.valueOf(userSlots.get(PATH_KEY))) && StringUtils.isNotEmpty(String.valueOf(userSlots.get(SESSION_ID_KEY)))) {
                sessionId = String.valueOf(userSlots.get(SESSION_ID_KEY));
            }
        } catch (Exception e) {
            log.error("{}-存储session映射失败, traceLog = {}", LOG_PREFIX, JSON.toJSONString(traceLog), e);
        }
        return sessionId;
    }

    /**
     * 保存task信息到会话
     *
     * @param sessionId       会话ID
     * @param traceLog        task记录
     * @param sessionCacheDTO session缓存
     */
    public void saveTaskInfo(String sessionId, TaskTraceLog traceLog, AidaOnlineSessionCacheDTO sessionCacheDTO) {
        // 1. 更新脚本构建
        // 1.1 task执行记录, 只保留过AI搭的
        String script = "";
        Map<String, Object> map = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(sessionCacheDTO.getInstanceIdList()) && sessionCacheDTO.getInstanceIdList().contains(traceLog.getInstanceId())) {
            script += EsScriptConstants.TASK_TRACE_LOG_ADD;
            // 更新会话信息中的字段
            map.put(EsScriptConstants.TASK_TRACE_LOG_TRIGGER_TIME, traceLog.getTriggerTime());
            map.put(EsScriptConstants.TASK_TRACE_LOG_TASK_NAME, traceLog.getTaskName());
            map.put(EsScriptConstants.TASK_TRACE_LOG_TASK_KEY, traceLog.getTaskKey());
            map.put(EsScriptConstants.TASK_TRACE_LOG_TASK_VERSION, traceLog.getTaskVersion());
            map.put(EsScriptConstants.TASK_TRACE_LOG_TASK_PROCESS_INSTANCE_ID, traceLog.getInstanceId());
            map.put(EsScriptConstants.TASK_TRACE_LOG_TASK_NODE_ID, traceLog.getNodeId());
            map.put(EsScriptConstants.TASK_TRACE_LOG_TASK_NODE_NAME, traceLog.getNodeName());
        }
        // 1.2 task异常退出
        if (EXCEPTION_EXIT.equals(traceLog.getReportType())) {
            map.put(EsScriptConstants.TASK_EXCEPTION_END_PARAM, Boolean.TRUE.toString());
            script += EsScriptConstants.TASK_EXCEPTION_END;
        }
        // 1.3 task超时退出
        if (TIME_OUT_EXIT.equals(traceLog.getReportType())) {
            map.put(EsScriptConstants.TASK_TIMEOUT_END_PARAM, Boolean.TRUE.toString());
            script += EsScriptConstants.TASK_TIMEOUT_END;
        }
        // 2. 更新会话信息
        if (StringUtils.isBlank(script)) {
            return;
        }
        eagleService.upsertByScript(sessionCacheDTO.getSessionIndex(), sessionId, script, map);
    }
}
