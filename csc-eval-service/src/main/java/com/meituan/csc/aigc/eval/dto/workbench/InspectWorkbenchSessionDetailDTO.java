package com.meituan.csc.aigc.eval.dto.workbench;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InspectWorkbenchSessionDetailDTO implements Serializable {
    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 时间
     */
    private Date time;

    /**
     * 类型 1-用户 2-客服
     */
    private Integer senderType;

    /**
     * 消息
     */
    private String message;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 原始消息，不展示，用于查看未支持的消息类型
     */
    private String originMessage;
    /**
     * 是否大模型
     */
    private Boolean isLlm;

    /**
     * 人工点赞点踩信息
     */
    private ManualInspect manualInspect;
    /**
     * 评论信息
     */
    private Comment comment;

    /**
     * 该消息的aida应用相关信息
     */
    private MessageLevelAidaAppInfoDTO messageLevelAidaAppInfo;


    /**
     * 基本信息
     */
    private List<InspectWorkbenchQueryDetailDTO.Detail.BasicInfo> basicInfo;


    @Data
    public static class ManualInspect implements Serializable {
        /**
         * 质检结果 1-赞 2-踩
         */
        private Integer inspectResult;

        /**
         * 质检人
         */
        private String inspectMis;
    }

    @Data
    public static class Comment implements Serializable {
        /**
         * 评论数量
         */
        private Integer commentCount;
    }
}
