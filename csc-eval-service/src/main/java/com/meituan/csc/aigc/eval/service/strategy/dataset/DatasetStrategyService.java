package com.meituan.csc.aigc.eval.service.strategy.dataset;

import com.meituan.csc.aigc.eval.dao.entity.EvalDatasetDetailPo;
import com.meituan.csc.aigc.eval.dto.dataset.TemplateFieldBindDTO;
import com.meituan.csc.aigc.eval.param.task.TaskParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DatasetStrategyService {

    /**
     * 解析数据集详情
     *
     * @param datasetDetailList 数据集列表
     * @param bindFieldsConfig  绑定字段配置
     * @param param
     */
    void parseDatasetDetail(List<EvalDatasetDetailPo> datasetDetailList, List<TemplateFieldBindDTO> bindFieldsConfig, TaskParam param);

    /**
     * 获取范式名称
     *
     * @return 范式名称
     */
    String getName();
}
