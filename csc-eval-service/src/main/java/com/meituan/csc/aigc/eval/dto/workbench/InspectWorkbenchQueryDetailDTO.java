package com.meituan.csc.aigc.eval.dto.workbench;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * InspectWorkbenchQueryDetailDTO
 *
 * <AUTHOR>
 */
@Data
public class InspectWorkbenchQueryDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 当前的大模型消息ID
     */
    private String id;

    /**
     * 信号信息
     */
    private List<Signal> signalList;

    /**
     * AI搭信号，按照大模型节点分组
     */
    private List<AidaSignal> signal;

    /**
     * AI搭所有信号
     */
    private AidaSignal allSignal;

    /**
     * 人工质检信息
     */
    private ManualInspect manualInspect;

    /**
     * 机器质检信息
     */
    private RobotInspect robotInspect;

    /**
     * 执行详情
     */
    private Detail detail;

    /**
     * 默认节点选择
     */
    private String selectedNodeId;

    private List<Detail.AidaTrace> aidaTraceList;

    /**
     * 该消息的aida应用相关信息
     */
    private MessageLevelAidaAppInfoDTO messageLevelAidaAppInfo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AidaSignal implements Serializable {
        /**
         * 节点ID
         */
        private String nodeId;
        /**
         * 节点名称
         */
        private String nodeName;
        /**
         * 加工前信号信息
         */
        private List<Signal> signalBeforeList;
        /**
         * 加工后信号信息
         */
        private List<Signal> signalAfterList;
    }

    @Data
    public static class Signal implements Serializable {
        /**
         * 信号key
         */
        private String signalKey;
        /**
         * 信号名称
         */
        private String signalName;

        /**
         * 信号值
         */
        private String signalValue;
        /**
         * 信号旧值
         */
        private String oldValue;
        /***
         * 是否变更
         */
        private Boolean isChange;
        /***
         * 是否收藏 true 收藏 false 未收藏
         */
        private Boolean isFavorite;

        public Signal() {
            this.isChange = false;
            this.isFavorite = false;
        }

        public Signal(String signalKey, String signalName, String signalValue) {
            this.signalKey = signalKey;
            this.signalName = signalName;
            this.signalValue = signalValue;
            this.isChange = false;
            this.isFavorite = false;
        }
    }
    @Data
    public static class ManualInspect implements Serializable {
        /**
         * 质检结果 1-赞 2-踩
         */
        private Integer inspectResult;

        /**
         * 质检人
         */
        private String inspectMis;
    }

    @Data
    public static class RobotInspect implements Serializable {
        /**
         * 质检结果 1-赞 2-踩
         */
        private String inspectResult;

        /**
         * 原因分析
         */
        private String reason;
    }

    @Data
    public static class Detail implements Serializable {
        /**
         * 基本信息
         */
        private List<BasicInfo> basicInfo;

        /**
         * 执行链路
         */
        private List<Trace> traceList;

        private List<AidaTrace> aidaTraceList;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class BasicInfo implements Serializable {
            /**
             * 展示名称
             */
            private String displayName;

            /**
             * 展示内容
             */
            private String displayContent;
        }

        @Data
        public static class Trace implements Serializable {
            /**
             * trace类型 AI搭为节点类型，PB为日志类型
             */
            private String type;

            /**
             * 输入
             */
            private String input;

            /**
             * 执行过程
             */
            private String process;

            /**
             * 输出
             */
            private String output;

            /**
             * 使用模型
             */
            private String model;
        }

        @Data
        public static class AidaTrace implements Serializable {
            /**
             * 应用ID
             */
            private String applicationId;

            /**
             * 节点ID
             */
            private String nodeId;

            /**
             * 节点类型
             */
            private String nodeType;

            /**
             * 节点名称
             */
            private String nodeName;

            /**
             * 延迟
             */
            private Long latency;

            /**
             * 令牌数
             */
            private Integer tokens;

            /**
             * 处理过程
             */
            private String processing;

            /**
             * 错误信息
             */
            private String errorMessage;

            /**
             * 模拟数据
             */
            private MockData mockData;

            /**
             * 状态
             */
            private Integer status;

            /**
             * 开始时间
             */
            private Long startTime;

            /**
             * 结束时间
             */
            private Long endTime;

            /**
             * 输入
             */
            private String input;

            /**
             * 输出
             */
            private String output;
        }

        @Data
        public static class MockData implements Serializable {
            /**
             * 是否模拟输入
             */
            private boolean isInputMock;

            /**
             * 是否模拟输出
             */
            private boolean isOutputMock;

            /**
             * 是否模拟
             */
            private boolean isMock;
        }
    }
}
