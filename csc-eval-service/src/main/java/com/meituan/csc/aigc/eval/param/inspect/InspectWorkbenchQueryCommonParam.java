package com.meituan.csc.aigc.eval.param.inspect;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InspectWorkbenchQueryCommonParam implements Serializable {
    /**
     * 消息ID，取详情中返回的id信息
     */
    private String messageId;

    /**
     * 大模型消息ID，取详情接口的ID
     */
    private String llmMessageId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 大模型会话ID，取会话数据的ID
     */
    private List<String> llmSessionId;

    /**
     * 平台类型，取会话数据的平台类型
     */
    private Integer platformType;
}
