package com.meituan.csc.aigc.eval.enums.workbench;

/**
 * <p>
 * 自定义配置类型枚举，用于表示不同的配置场景。
 * </p>
 *
 * <ul>
 *     <li>SIGNAL_ALL_SORT：信号全局排序</li>
 *     <li>SIGNAL_NODE_SORT：信号节点排序</li>
 *     <li>SELECTED_NODE：默认展示节点</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public enum CustomConfigTypeEnum {

    /**
     * 信号全局排序，用于全局信号的统一排序配置
     */
    SIGNAL_ALL_SORT("signal_all_sort", "信号全局排序"),

    /**
     * 信号节点排序，用于特定节点的信号排序配置
     */
    SIGNAL_NODE_SORT("signal_node_sort", "信号节点排序"),
    /**
     * 信号节点排序，用于特定节点的信号排序配置
     */
    SIGNAL_FAVORITE("signal_favorite", "信号收藏"),

    /**
     * 默认信号展示节点，用于配置默认展示的模型节点
     */
    SELECTED_NODE("selected_node", "默认信号展示节点"),
    /**
     * 默认模型选择展示节点，用于配置默认展示的模型节点
     */
    SELECTED_LLM_TREE("selected_llm_tree", "默认模型选择展示节点"),
    /**
     * 参考调试窗口宽度设置
     */
    REFERENCE_DEBUGGING_WINDOW_WIDTH("reference_debugging_window_width", "参考调试窗口宽度设置"),

    /**
     * 在线会话分析常用筛选条件
     */
    WORKBENCH_SESSION_ONLINE_CONDITION_CONFIG("workbench_session_online_condition_config", "在线会话分析常用筛选条件配置"),

    /**
     * 电话会话分析常用筛选条件
     */
    WORKBENCH_SESSION_IVR_CONDITION_CONFIG("workbench_session_ivr_condition_config", "电话会话分析常用筛选条件配置"),
    ;

    /**
     * 配置类型编码，唯一标识每种配置类型
     */
    private final String code;

    /**
     * 配置类型描述，便于理解配置的具体用途
     */
    private final String description;

    /**
     * 枚举类的构造方法，用于初始化配置类型的编码和描述
     *
     * @param code        配置类型编码
     * @param description 配置类型描述
     */
    CustomConfigTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取配置类型编码
     *
     * @return 配置类型编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取配置类型描述
     *
     * @return 配置类型描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据编码查找对应的枚举类型
     *
     * @param code 配置类型编码
     * @return 对应的配置类型枚举
     * @throws IllegalArgumentException 如果编码无效，抛出异常
     */
    public static CustomConfigTypeEnum fromCode(String code) {
        for (CustomConfigTypeEnum type : CustomConfigTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}

