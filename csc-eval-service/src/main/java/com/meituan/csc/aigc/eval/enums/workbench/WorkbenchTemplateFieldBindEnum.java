package com.meituan.csc.aigc.eval.enums.workbench;

import com.meituan.csc.aigc.eval.dto.dataset.SingleTemplateFieldBindDTO;
import com.meituan.csc.aigc.eval.enums.dataset.DataTypeEnum;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public enum WorkbenchTemplateFieldBindEnum {
    SESSION_ID("session_id", "会话id", DataTypeEnum.SYSTEM_VARIABLE),
    MESSAGE_ID("message_id", "消息id", DataTypeEnum.APPLICATION_VARIABLE),
    ORDER_ID("order_id", "订单id", DataTypeEnum.APPLICATION_VARIABLE),
    USER_ID("user_id", "用户id", DataTypeEnum.APPLICATION_VARIABLE),
    HISTORY("history", "历史对话", DataTypeEnum.INPUT),
    INPUT("input", "输入内容", DataTypeEnum.INPUT),
    OUTPUT("output", "输出内容", DataTypeEnum.OUTPUT),
    EXPECTED_RESULT("expected_result", "预期结果", DataTypeEnum.EXPECTED_RESULT),
    FEEDBACK("feedback", "反馈", DataTypeEnum.APPLICATION_VARIABLE),
    FEEDBACK_MIS("feedback_mis", "反馈人", DataTypeEnum.APPLICATION_VARIABLE);

    private final String code;
    private final String info;
    private final DataTypeEnum dataType;

    WorkbenchTemplateFieldBindEnum(String code, String info, DataTypeEnum dataType) {
        this.code = code;
        this.info = info;
        this.dataType = dataType;
    }


    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public DataTypeEnum getDataType() {
        return dataType;
    }

    public SingleTemplateFieldBindDTO toDTO() {
        return new SingleTemplateFieldBindDTO(this.info, this.dataType);
    }

    public static List<SingleTemplateFieldBindDTO> getAllFieldBinds() {
        return Arrays.stream(WorkbenchTemplateFieldBindEnum.values())
                .map(WorkbenchTemplateFieldBindEnum::toDTO)
                .collect(Collectors.toList());
    }

    public static List<String> getAllCode() {
        return Arrays.stream(WorkbenchTemplateFieldBindEnum.values())
                .map(WorkbenchTemplateFieldBindEnum::getCode)
                .collect(Collectors.toList());
    }

    public static List<String> getAllInfo() {
        return Arrays.stream(WorkbenchTemplateFieldBindEnum.values())
                .map(WorkbenchTemplateFieldBindEnum::getInfo)
                .collect(Collectors.toList());
    }
}
